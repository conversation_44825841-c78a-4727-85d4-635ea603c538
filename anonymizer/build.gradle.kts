plugins {
    kotlin("jvm")
    application
    id("org.beryx.runtime") version "1.13.0"
}

dependencies {
    implementation("org.jetbrains.kotlin:kotlin-stdlib")
    implementation("org.redundent:kotlin-xml-builder:1.8.0")
    implementation("com.github.ajalt.clikt:clikt:3.5.2")

    testImplementation("org.jetbrains.kotlin:kotlin-test")
    testImplementation("org.junit.jupiter:junit-jupiter:5.9.2")
}

application {
    mainClass.set("de.fishbyte.mrblister.anonymizer.MainKt")
}

runtime {
    options.set(listOf("--strip-debug", "--compress", "2", "--no-header-files", "--no-man-pages"))

    modules.set(listOf("java.base", "java.desktop"))

    jpackage {
        // Windows specific options
        if (org.gradle.internal.os.OperatingSystem.current().isWindows) {
            installerType = "exe"
            installerOptions = listOf("--win-per-user-install", "--win-dir-chooser", "--win-menu")
        }

        // MacOS specific options
        if (org.gradle.internal.os.OperatingSystem.current().isMacOsX) {
            installerType = "dmg"
            installerOptions = listOf("--mac-package-name", "MrBlister Anonymizer")
        }
    }
}

tasks.test {
    useJUnitPlatform()
}