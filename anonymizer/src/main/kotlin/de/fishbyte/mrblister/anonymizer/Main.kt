package de.fishbyte.mrblister.anonymizer

import com.github.ajalt.clikt.core.CliktCommand
import com.github.ajalt.clikt.parameters.arguments.argument
import com.github.ajalt.clikt.parameters.types.file
import java.io.File
import javax.xml.parsers.DocumentBuilderFactory
import javax.xml.transform.TransformerFactory
import javax.xml.transform.dom.DOMSource
import javax.xml.transform.stream.StreamResult

class Anonymizer : CliktCommand() {
    private val inputDir by argument().file(mustExist = true, canBeFile = false)
    private val outputDir by argument().file(canBeFile = false)

    private val firstNames = listOf(
        "Ziggy", "Pickle", "Waffle", "Noodle", "B<PERSON>ble", "Squish", "Wobble", "Floof", "Doodle", "Biscuit",
        "Wiggle", "Pancake", "Muffin", "Je<PERSON>", "<PERSON>ig<PERSON>", "Snicker", "Bumble", "<PERSON>ippy", "Pudding", "Wombat",
        "Noodles", "Pickles", "Snuggles", "Waddles", "Wiggles", "<PERSON><PERSON>bles", "<PERSON>rkle", "<PERSON>kle", "<PERSON>prinkle", "<PERSON>ephyr",
        "<PERSON>igzag", "Waffles", "<PERSON>odles", "Biscuits", "Muffins", "Pickles", "Giggles", "Wobbles", "Doodles", "Floofy",
        "Squishes", "Wiggles", "<PERSON>cakes", "Jellies", "<PERSON>umbles", "<PERSON>ippies", "Puddings", "Snickers", "Waddles", "Twinkles"
    )

    private val lastNames = listOf(
        "Snazzleworth", "Pickleheimer", "Wafflington", "Noodlington", "Bubblesworth", "Squishington", "Wobbleston", "Floofington", "Doodleworth", "Biscuitton",
        "Wigglebottom", "Pancakington", "Muffinworth", "Jellysworth", "Gigglesworth", "Snickerton", "Bumbleworth", "Zippington", "Puddingworth", "Wombatton",
        "Noodlesworth", "Pickleworth", "Snuggleton", "Waddlesworth", "Wiggleworth", "Bubbleston", "Sparkleston", "Twinkleworth", "Sprinkleton", "Zephyrworth",
        "Zigzagton", "Waffleston", "Noodleton", "Biscuitworth", "Muffinston", "Pickleston", "Giggleston", "Wobbleworth", "Doodleston", "Floofworth",
        "Squishworth", "Wiggleston", "Pancakeworth", "Jellyston", "Bumbleston", "Zippyworth", "Puddington", "Snickerton", "Waddleton", "Twinkleston"
    )

    private val streetNames = listOf(
        "Lollipop Lane", "Bubblegum Boulevard", "Marshmallow Mile", "Rainbow Road", "Unicorn Avenue",
        "Giggle Grove", "Waffle Way", "Pickle Path", "Noodle Nook", "Bouncy Boulevard",
        "Jellybean Junction", "Cupcake Court", "Donut Drive", "Peppermint Place", "Candy Cane Circle",
        "Silly Street", "Whimsy Way", "Muffin Mall", "Pudding Path", "Toffee Trail",
        "Cookie Corner", "Sprinkle Street", "Chocolate Chase", "Banana Boulevard", "Ice Cream Isle"
    )

    private val townNames = listOf(
        "Giggleton", "Chuckleburg", "Sillyton", "Waffleville", "Noodleopolis",
        "Picklewick", "Bubbletown", "Snickerville", "Jellyville", "Muffinton",
        "Doodleburg", "Wobbleshire", "Floofington", "Squishville", "Twinkletown",
        "Sprinkleburg", "Goofyville", "Zanyton", "Whimsyburg", "Chortleville",
        "Snazzleton", "Giggleshire", "Bouncyburg", "Wiggleton", "Zippytown"
    )

    override fun run() {
        // Get all XML files from input directory
        val xmlFiles = inputDir.walk()
            .filter { it.isFile && it.extension.equals("xml", ignoreCase = true) }
            .toList()

        val dbFactory = DocumentBuilderFactory.newInstance()
        val dBuilder = dbFactory.newDocumentBuilder()

        xmlFiles.forEach { inputFile ->
            val doc = dBuilder.parse(inputFile)

            // Create output file path maintaining same name
            val outputFile = File(outputDir, "anonymized_${inputFile.name}")
            outputFile.parentFile.mkdirs()

            // Anonymize with random silly names
            doc.getElementsByTagName("Name1").forEach { it.textContent = firstNames.random() }
            doc.getElementsByTagName("Name2").forEach { it.textContent = lastNames.random() }

            doc.getElementsByTagName("Birthday").forEach { it.textContent = "1970-01-01" }
            doc.getElementsByTagName("Street").forEach { it.textContent = "${(1..200).random()} ${streetNames.random()}" }
            doc.getElementsByTagName("Town").forEach { it.textContent = townNames.random() }
            doc.getElementsByTagName("ZipCode").forEach { it.textContent = "1234" }
            doc.getElementsByTagName("Phone").forEach { it.textContent = "01631737743" }
            doc.getElementsByTagName("FAX").forEach { it.textContent = "052116391643" }
            doc.getElementsByTagName("Email").forEach { it.textContent = firstNames.random() + "." + lastNames.random() + "@muster.com" }

            // Get all NursingHome elements
            val nursingHomes = doc.getElementsByTagName("NursingHome")

            // For each NursingHome, find and anonymize Name elements
            nursingHomes.forEach { nursingHome ->
                val names = (nursingHome as org.w3c.dom.Element).getElementsByTagName("Name")
                names.forEach { it.textContent = "Pflegedienst Herzlich" }
                val stations = (nursingHome as org.w3c.dom.Element).getElementsByTagName("Station")
                stations.forEach { it.textContent = "Tagespflege" }
            }

            // Get all Patient elements
            doc.getElementsByTagName("Patient").forEach { patientNode ->
                val patient = patientNode as org.w3c.dom.Element
                // Get only direct children named "ID"
                for (i in 0 until patient.childNodes.length) {
                    val child = patient.childNodes.item(i)
                    if (child.nodeType == org.w3c.dom.Node.ELEMENT_NODE && child.nodeName == "ID") {
                        child.textContent = String.format("%07d", (1000000..9999999).random())
                        break  // Only handle the first direct ID child
                    }
                }
            }

            // Get all the Job elements
            doc.getElementsByTagName("Job").forEach { jobNodepatientNode ->
                val patient = jobNodepatientNode as org.w3c.dom.Element
                // Get only direct children named "ID"
                for (i in 0 until patient.childNodes.length) {
                    val child = patient.childNodes.item(i)
                    if (child.nodeType == org.w3c.dom.Node.ELEMENT_NODE && child.nodeName == "ID") {
                        child.textContent = String.format("%06d", (100000..999999).random())
                        break  // Only handle the first direct ID child
                    }
                }
            }

            // Write the transformed XML
            val transformer = TransformerFactory.newInstance().newTransformer()
            transformer.transform(DOMSource(doc), StreamResult(outputFile))

            echo("File anonymized successfully: ${outputFile.absolutePath}")
        }

        echo("Processed ${xmlFiles.size} XML files")
    }
}fun main(args: Array<String>) = Anonymizer().main(args)

private fun org.w3c.dom.NodeList.forEach(action: (org.w3c.dom.Node) -> Unit) {
    for (i in 0 until length) {
        action(item(i))
    }
}

// :anonymizer:run --args="/Volumes/HOME/Users/<USER>/Workspace/MrBlister/sample/Gaby_4er_Blisterkunden_03602_11.11.24_12.56.xml /Volumes/HOME/Users/<USER>/Desktop"

