

Ein ähnliches Gerät der BlisterJacky https://www.ecoblister.de/trends-apps-automatisierung/blisterjacky/. Hier wird da ganze mit einem Beamer gelöst wenn die Blister nicht durchsichtig sind. Das sollte ja aber im Prinzip keine Unterschied für die SW machen ob Bildschirm oder Beamer.



Hier mal ein paar Requirements die mir so einfallen:



    Die SW soll unter Windows laufen. Evtl. wäre auch der Betrieb auf einem Raspberry Pi denkbar nach Rücksprache.
    Die SW soll lokal auf einem Rechner laufen. Keine Anbindung ans Internet.
    Es soll eine Standard USB Kamera genutzt werden.
    Es soll ein Monitor oder Beamer benutzt werden können.
    Die Größe und Auflösung soll an die vorhanden Fill2Light HW anpassbar sein.
    Die Position der Leuchtrechtecke soll verschiebbar sein, so dass man die Position auf verschiede HW anpassen kann (erstmal optional).
    Es sollen die 4x7 und 5x7 Becherblister von I-Meds unterstützt werden.
    Es soll möglich sein später weitere Blister mit anderen Maßen zu unterstützen.
    Es soll automatsich erkannt werden, ob die 7x4 oder 7x5 Blister genutzt werden.
    Wenn auch andere Blister unterstützt werden ist dies evtl von Heim zu Heim oder Kunde zu Kunde unterschiedlich. Hier soll nach Rücksprache die Information aus der XML Datei geholt werden oder muss in der SW konfigurierbar sein.
    Die einzulesene XML Datei soll von einem Netzlaufwerk gelesen werden.
    Die zu speichernden Fotos sollen lokal oder auf einem Netzlaufwerk abgelegt werden.
    Die Fotos sollen in einem Strukturierten Verzeichnisbaum / oder Datenbank abgelegt werden, so dass man Sie nach Datum, Heim, Patient, Blisterzeitraum finden kann.
    Es sollen mindestens bis zu 100 Benutzer angelegt werden können.
    Die Benutzer müssen sich mit Passwort anmelden (keine Passwortregeln).
    Es sollen mehrere XML Dateien nacheinander oder auf einmal eingelesen werden können.
    Die einzelnen Blisteraufträge (XML Dateien) sollen nach jedem Patienten unterbrochen werden können und dann als unvollständig in der Liste der Aufträge erkennbar sein und von dort erneut gestartet werden können.
    Die bereits durchgeführten Aufträge sollen in der Liste für die letzten 4 Wochen verbleiben entweder ausgegraut oder ausgeblendet durch eine Checkbox.
    Nach dem Einlesen der XML Datei soll eine Liste mit allen Namen der Patienten aufgelistet werden.
    Nach einlesen der Patientennummer per Barcode soll der Blistervorgang für den Patienten gestartet werden.
    Ist der zugehörige Patient nicht in der Liste der Patienten soll es eine Fehlermeldung geben, dass der Patient nicht im aktuellen Blisterauftrag ist.
    Es soll möglich sein den zu blisternen Patienten manuell aus der Liste auszuwählen.
    Wird der Patient manuell ausgewählt soll es einen Warnhinweis geben der lautet: „Bitte Barcode des Patienten scannen. Ein manuelles auswählen ist nur in Ausnahmefällen erlaubt.“ Mit der Option „zurück zum Scannen“ oder „Blistervorgang ohne scannen starten“
    Ein manuelles starten eines Blistervorgangs soll in einer Log Datei protokolliert werden mit Name, Datum und Uhrzeit des Benutzers und um welchen Patienten, Heim, Blisterwoche  es sich handelte.
    Wird der Blistervorgang manuell gestartet soll es einen Timer von x Sekunden (x = 0-99) geben. In der einen Warte Anzeige kommt „Blistervorgang manuell gestartet, bitte warten Sie x Sekunden“ Bei x=0 soll diese Meldung nicht erscheinen. X soll konfigurierbar sein.
    Nach dem starten des Blistervorgangs sollen die Medikamnte aufgelistet werden.
    Die Medikamentenpackungen müssen abgescannt werden.
    Wir eine Medikamentenpackung absgescannt die nicht im Blisterauftrag ist oder wurde die Packung bereits abgescannt soll eine entsprechnde Fehlermeldung angezeigt werden. Bei bereits abgescannt soll es die Möglichkeit geben trotzdem noch einmal die Packung zu stellen.
    Die Packung soll auch manuell aus der Liste auswählbar sein.
    Wird die Packung manuell ausgewählt soll es einen Warnhinweis geben. „Es muss die Packung per Barcode gescannt werden. Nur in Ausnahmefällen ist eine manuelle Auswahl erlaubt.“ Auswahlmöglichkeit „zurück zum Scannen der Packung“ Packung manuell auswählen“.
    Ein manuelles auswählen der Packung soll in einer Log Datei protokolliert werden mit Name, Datum und Uhrzeit des Benutzers und um welchen Patienten, Heim, Blisterwoche  es sich handelte.
    Wird die Packung manuell ausgewählt soll es einen Timer von y Sekunden (y = 0-99) geben. In der einen Warte Anzeige kommt „Packung manuell ausgewählt, bitte warten Sie y Sekunden“ Bei y=0 soll diese Meldung nicht erscheinen. Y soll konfigurierbar sein.
    Der Blistervorgang soll analog zu dem bereit gestellten Video verlaufen.
    Die in der XML-Datei enthaltenen Tablettenbilder sollen angezeigt werden.
    Alle Informationen zum aktuellen Blisterauftrag sollen angezeigt werden (Heim, Patient, …).
    Je nach Anzahl der zu stellenden Tabletten soll der Einnahmezeitpunkt eine andere Farbe haben.
    Bei halben Tabletten soll der Einnahmezeitpunkt blinken.
    Wird eine Tablette nicht täglich zum gleichen Einnahmezeitpunkt gegeben, soll dies optisch hervorgehoben werden.
    Die Hinweise aus der XML Datei. Sollen zu jedem Medikament mit angezeigt werden. Z.B. „Im Blister Stellen“.
    Die Zeit nach dem Drücken der LEERTASTE und dem speichern des Fotos soll konfigurierbar sein.
    Es soll konfigurierbar sein, ob nach dem letzten Medikament gleich das Abschlussfoto gemacht wird oder erst noch eins für das letzte Medikament.
    Die gemachten Fotos sollen für z-Monate gespeichert und danach automatisch gelöscht werden. Z soll konfigurierbar sein.
    Die gemachten Fotos sollen wieder auffindbar sein. Entweder manuell in einer Verzeichnisstruktur oder über eine Suchmaske und Anzeige.
51: Bei einem Packungswechsel (Tabletten eine Packung sind leer und es muss eine neue Packung angebrochen werden) soll auch ein Abscannen erforderlich sein. Egal ob die Firma der Packung sich ändert, oder genau die gleiche Packung gestellt werden soll.



44. Der Warnhinweis bei manuellem Auswählen des Blisterauftrags soll ganz ausgeschaltet werden können (konfigurierbar).

45. Der Warnhinweis bei manuellem Auswählen des Medikaments soll ganz ausgeschaltet werden können (konfigurierbar).

46. OPTIONAL (zukünftige Version): Es soll der DataMatrix Code der Packungen ausgelesen werden (anstatt der Strichcode) und die Packung aus dem Securpharm Server ausgebucht werden (Per Button „Ausbuchen“).

47. Beim Laden des Blisterauftrags vom Netzlaufwerk soll die Datei vom Netzlaufwerk in ein lokales Verzeichnis verschoben werden. (es kann die Datei dann nicht von einem weiteren Arbeitsplatz aufgerufen werden).

48. Das Unterbrechen eines Blisterauftrags eines Patienten soll jederzeit möglich sein.

49. Unterbrochene Blisteraufträge sollen in der Liste der Patienten als unterbrochen gekennzeichnet sein und wieder aufrufbar sein.

50. Das Blistern von undurchsichtigen Blistern soll möglich sein (Beamer anstatt Monitor).