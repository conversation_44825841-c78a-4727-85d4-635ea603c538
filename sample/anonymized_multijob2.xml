<?xml version="1.0" encoding="utf-8" standalone="no"?><BlisterOrder xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <ID>bj00000004064</ID>
  <Version/>
  <CreateDate>2025-02-14</CreateDate>
  <CreateTime>12:57:10.8742462+01:00</CreateTime>
  <Drugs>
    <Drug>
      <ID>**********</ID>
      <Name>Novaminsulfon Lichten500mg</Name>
      <Manufacturer>Zentiva Pharma GmbH</Manufacturer>
      <MedicineSubstance>Metamizol natrium-1-Was<PERSON></MedicineSubstance>
      <Divisible>half</Divisible>
      <Color>weiß</Color>
      <Form>Länglich</Form>
      <FrontMarking>|</FrontMarking>
      <RearMarking>|</RearMarking>
      <FotoData>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</FotoData>
      <PackingTypes>
        <PackingType>
          <ID>262450</ID>
          <Size>10</Size>
        </PackingType>
        <PackingType>
          <ID>4906602</ID>
          <Size>20</Size>
        </PackingType>
        <PackingType>
          <ID>262467</ID>
          <Size>30</Size>
        </PackingType>
        <PackingType>
          <ID>1798000</ID>
          <Size>50</Size>
        </PackingType>
      </PackingTypes>
    </Drug>
    <Drug>
      <ID>3338860012</ID>
      <Name>Metoprolol 100 1A Pharma</Name>
      <Manufacturer>1 A Pharma GmbH</Manufacturer>
      <MedicineSubstance>Metoprolol tartrat</MedicineSubstance>
      <Divisible>half</Divisible>
      <Color>weiß</Color>
      <Form>Achteck</Form>
      <FrontMarking>+</FrontMarking>
      <FotoData>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</FotoData>
      <PackingTypes>
        <PackingType>
          <ID>8533902</ID>
          <Size>30</Size>
        </PackingType>
        <PackingType>
          <ID>8533919</ID>
          <Size>50</Size>
        </PackingType>
        <PackingType>
          <ID>8533925</ID>
          <Size>100</Size>
        </PackingType>
      </PackingTypes>
    </Drug>
    <Drug>
      <ID>3348710500</ID>
      <Name>Levodopa P Bens AL 50/12.5</Name>
      <Manufacturer>ALIUD Pharma GmbH</Manufacturer>
      <MedicineSubstance>Levodopa#Benserazid</MedicineSubstance>
      <Divisible>undefined</Divisible>
      <Color>blau, grau</Color>
      <Form>Länglich</Form>
      <FotoData>/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAcFBQYFBAcGBQYIBwcIChELCgkJChUPEAwRGBUaGRgVGBcbHichGx0lHRcYIi4iJSgpKywrGiAvMy8qMicqKyr/2wBDAQcICAoJChQLCxQqHBgcKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKir/wAARCACWAJYDASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwC40sEQUSvEh2g4YgUn2m0/57W//fa1WuNMtL+4D3UbMyxqoIYjj/JqP/hHdN/54t/38NZ6CLv2m0/57W//AH2tH2m0/wCe1v8A99rVL/hHdN/54t/38NH/AAjum/8APFv+/ho0Au/abTtNB/30tP3Rf7H5Cs19B06Jd6QtuUgg+YeOa09pPO9+fegBN0X+x+Qo3Rf7H5Cl2/7b/nRt/wBt/wA6AE3Rf7H5ClUxt93YcegFG3/bf86jB2XDcMcgck+mT/WgCXav91fyFG1f7q/kKjWbcyjYwz69qloATav91fyFG1f7q/kKWikAm1f7q/kKNq/3V/IUtFACbV/ur+Qo2r/dX8hS0UARzKvl/dXr6Cilm/1f40UAOX7i/QfypaRfuL9B/KloAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigBk3+r/Giib/V/jRQA5fuL9B/KlpF+4v0H8qWgAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKAGTf6v8aKJv9X+NFADl+4v0H8qWkX7i/QfypaACiiop5vKXjqa1p05VJcsTCvXp4eHPUehKBnpS7W/un8qis3k8w7mPI4q8Hk/56N+ddn1JrqeR/bEXtD8SttJ6A/lS+W+M7Gx9KaHfzWBdjz1yaeWY/xt/wB9Gn9R8w/thP7P4ibGIyFOPXFGxgpJU4HU46Ug3Do7Aem41o6SzG4kBYkGM8E5HUVE8HyRcr7GlHNFUmocu5m5FGa6jyV/uJ/3yKT7OneNP++BXncx7djmM0V1ItIj1ijP/ABThY256wRH/gAo5kFjlKK686LY3K7XhERPR4ztI/pWBq2kT6RcBZSHifmOUdG/wNNO4WsZ9FFFMQyb/V/jRRN/q/xooAcv3F+g/lS0i/cX6D+VLQAVXliLNubnFTtwuaWcgQEj0r1MG1GDZ4GZxdSrFPZIhjlAdR3FWRKCetczBeubtt7dMirsd5ukxmvS5dD5iVVc7Re8z96eakDZjJzz6ViC9Pntzxmpftn+1VcrIVVGqJPl5Oa09GcG5f8A65n+lcsb3HfNbHh+633MvP8AyyP8xWFdfupeh2YKoniYLzR1genB6oLNx1p4mr5s++L4epFas8TVMkvvUMpGtAflq9cafFq/h+7tGGZUXzYm9CP8/kTWZavlRWzYTiCQv1G0g/iKqImeW0U6QgzOV6FiR9M02tCRk3+r/Giib/V/jRQA5fuL9B/KlpF+4v0H8qWgAIypB7isHUru5VfszHaF4JXqwreqC7s4ryPbJww6MOoroo1vZvXY48Vhvbx03Rxsb+RMferKXWGzmpb3w1fGXdbSRyLj+I4qqPDurg/cj/77r1IYuFtWfM18pqyleKG/aP3hJpxugBknA9aiPh3WVY/ukb6NThoutBSv2ZSp9Wq1i6fczllFa+xJ9pBGQcitrw3c4muGzwIv/ZhXP/2FrH/PsB/wKtTSNL1OBbgSxrGWVQpY9ecn+VZ1sTCVKST6G2Gy2rSxEJ20TR1i3q+tSLeKf4hWALK+3ZMkeKUWV/8A89Y68HU+xOkS6H94fnU8dxk8EfnXNRwXqdZU/WrUZul+86frUtMrQ7KymyoqXV9UFlpjKjfvpgVQenqa5mHUpYI8Lhm9+lVpp5LiUyTMWY9zVRT6iZH0oooqyRk3+r/Giib/AFf40UAOX7i/QfypaRfuL9B/KloAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigBk3+r/ABoom/1f40UAOX7i/QfypaRfuL9B/KloAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigBk3+r/Giib/V/jRQA5fuL9B/KlpF+4v0H8qWgAooooAKKKKACiiigAooqpeKzSKSrNHjovr+R9qYFulwfSs+dbgpCIlY+WTLgsQcA8D3OM8VX2N5hJRyhc+YAjDjzARn149KLAa9Lg+lZZWbcjRRv5ULl1BJBKk4xjvxu49xRJbyIrLbREFZXkzuIyF+6Pfr0osBpMyoMscDIHPqaXFQXRzChHGZIzz/vCo71VMsfmI7LtfGwE/Nxjp3oAt0VnGW9VSCWDKPm/d5A6cg45J59celAlvBlgpUDBKsg+bOM5PagC9N/q/xoqrbTPNbuXcvtcAErt/hBP6migCysybF+90Hal85P9r8qKKQB5yf7X5Uecn+1+VFFAB5yf7X5Uecn+1+VFFAB5yf7X5Uecn+1+VFFAB5yf7X5Uecn+1+VFFAB5yf7X5Uecv8AtflRRQAecn+1+VHnJ/tflRRQA13icAOGIBB6dxyKd5yf7X5UUUAHnJ/tflR5yf7X5UUUARO8UcIWNdo3dFUAUUUUwP/Z</FotoData>
      <PackingTypes>
        <PackingType>
          <ID>12565724</ID>
          <Size>50</Size>
        </PackingType>
        <PackingType>
          <ID>12565730</ID>
          <Size>100</Size>
        </PackingType>
      </PackingTypes>
    </Drug>
    <Drug>
      <ID>3340347100</ID>
      <Name>Folsaeure Ratiopharm 5mg</Name>
      <Manufacturer>ratiopharm GmbH</Manufacturer>
      <MedicineSubstance>Folsäure</MedicineSubstance>
      <Divisible>no</Divisible>
      <Color>gelb</Color>
      <Form>Rund</Form>
      <FrontMarking>|</FrontMarking>
      <FotoData>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</FotoData>
      <PackingTypes>
        <PackingType>
          <ID>3971365</ID>
          <Size>20</Size>
        </PackingType>
        <PackingType>
          <ID>3971388</ID>
          <Size>50</Size>
        </PackingType>
        <PackingType>
          <ID>4010113</ID>
          <Size>100</Size>
        </PackingType>
      </PackingTypes>
    </Drug>
    <Drug>
      <ID>3052520031</ID>
      <Name>Madopar Depot</Name>
      <Manufacturer>Roche Pharma AG</Manufacturer>
      <MedicineSubstance>Levodopa#Benserazid</MedicineSubstance>
      <Divisible>no</Divisible>
      <Color>grün, blau</Color>
      <Form>Länglich</Form>
      <FrontMarking>ROCHE</FrontMarking>
      <RearMarking>ROCHE</RearMarking>
      <FotoData>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</FotoData>
      <PackingTypes>
        <PackingType>
          <ID>4759756</ID>
          <Size>20</Size>
        </PackingType>
        <PackingType>
          <ID>4759779</ID>
          <Size>100</Size>
        </PackingType>
      </PackingTypes>
    </Drug>
  </Drugs>
  <Doctors>
    <Doctor>
      <ID>1</ID>
      <Name1>Zephyr</Name1>
      <Name2>Floofworth</Name2>
      <Street>107 Silly Street</Street>
      <ZipCode>1234</ZipCode>
      <Town>Wiggleton</Town>
      <Phone>01631737743</Phone>
    </Doctor>
  </Doctors>
  <Pharmacy>
    <ID>693840</ID>
    <Name1>Bumbles</Name1>
    <Name2>Pickleheimer</Name2>
    <Street>118 Pickle Path</Street>
    <ZipCode>1234</ZipCode>
    <Town>Snazzleton</Town>
    <Phone>01631737743</Phone>
    <FAX>052116391643</FAX>
    <Email><EMAIL></Email>
  </Pharmacy>
  <Patients>
    <Patient>
      <ID>3680389</ID>
      <Title/>
      <Name1>Bumbles</Name1>
      <Name2>Jellysworth</Name2>
      <Birthday>1970-01-01</Birthday>
      <NursingHome>
        <Name>Pflegedienst Herzlich</Name>
        <Station>Tagespflege</Station>
      </NursingHome>
      <Jobs>
        <Job>
          <ID>731702</ID>
          <Type>WeekA4</Type>
          <Medications>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-17</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LMT5461</ChargeNumber>
                  <ExpirationDate>2026-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3338860012</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-17</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>ET897012</ChargeNumber>
                  <ExpirationDate>2027-08-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-17</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-17</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LMT5461</ChargeNumber>
                  <ExpirationDate>2026-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3340347100</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-17</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>5896MX</ChargeNumber>
                  <ExpirationDate>2029-02-28</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-17</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-17</TakingDate>
              <TakingTime>17:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-18</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LMT5461</ChargeNumber>
                  <ExpirationDate>2026-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3338860012</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-18</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>ET897012</ChargeNumber>
                  <ExpirationDate>2027-08-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-18</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-18</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LMT5461</ChargeNumber>
                  <ExpirationDate>2026-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3340347100</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-18</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>5896MX</ChargeNumber>
                  <ExpirationDate>2029-02-28</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-18</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-18</TakingDate>
              <TakingTime>17:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-19</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LMT5461</ChargeNumber>
                  <ExpirationDate>2026-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3338860012</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-19</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>ET897012</ChargeNumber>
                  <ExpirationDate>2027-08-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-19</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-19</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LMT5461</ChargeNumber>
                  <ExpirationDate>2026-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3340347100</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-19</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>5896MX</ChargeNumber>
                  <ExpirationDate>2029-02-28</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-19</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-19</TakingDate>
              <TakingTime>17:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-20</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3338860012</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-20</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>ET897012</ChargeNumber>
                  <ExpirationDate>2027-08-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-20</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-20</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3340347100</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-20</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>5896MX</ChargeNumber>
                  <ExpirationDate>2029-02-28</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-20</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-20</TakingDate>
              <TakingTime>17:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-21</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3338860012</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-21</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>ET897012</ChargeNumber>
                  <ExpirationDate>2027-08-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-21</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-21</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3340347100</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-21</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>5896MX</ChargeNumber>
                  <ExpirationDate>2029-02-28</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-21</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-21</TakingDate>
              <TakingTime>17:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3338860012</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>4</Dosage>
              <TakingDate>2025-02-23</TakingDate>
              <TakingTime>00:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Information>Tabl. zum Teilen</Information>
              <Packages>
                <Package>
                  <ChargeNumber>ETG5612</ChargeNumber>
                  <ExpirationDate>2025-09-30</ExpirationDate>
                  <Quantity>4</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-23</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3338860012</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-23</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>ET897012</ChargeNumber>
                  <ExpirationDate>2027-08-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-23</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-23</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3340347100</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-23</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>5896MX</ChargeNumber>
                  <ExpirationDate>2029-02-28</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-23</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-23</TakingDate>
              <TakingTime>17:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3338860012</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>4</Dosage>
              <TakingDate>2025-03-02</TakingDate>
              <TakingTime>00:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Information>Tabl. zum Teilen</Information>
              <Packages>
                <Package>
                  <ChargeNumber>ETG5612</ChargeNumber>
                  <ExpirationDate>2025-09-30</ExpirationDate>
                  <Quantity>4</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-03-02</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3338860012</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-03-02</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>ET897012</ChargeNumber>
                  <ExpirationDate>2027-08-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-03-02</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-03-02</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3340347100</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-03-02</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>5896MX</ChargeNumber>
                  <ExpirationDate>2029-02-28</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-03-02</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-03-02</TakingDate>
              <TakingTime>17:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
          </Medications>
        </Job>
        <Job>
          <ID>636132</ID>
          <Type>WeekA4</Type>
          <Medications>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-22</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3338860012</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-22</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>ET897012</ChargeNumber>
                  <ExpirationDate>2027-08-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-22</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-22</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3340347100</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-22</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>5896MX</ChargeNumber>
                  <ExpirationDate>2029-02-28</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-22</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-22</TakingDate>
              <TakingTime>17:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3052520031</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-22</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>B54901</ChargeNumber>
                  <ExpirationDate>2024-05-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-22</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-22</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-24</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3338860012</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-24</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>ET897012</ChargeNumber>
                  <ExpirationDate>2027-08-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-24</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-24</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3340347100</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-24</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>5896MX</ChargeNumber>
                  <ExpirationDate>2029-02-28</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-24</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-24</TakingDate>
              <TakingTime>17:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3052520031</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-24</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>B54901</ChargeNumber>
                  <ExpirationDate>2024-05-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-24</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-24</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-25</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3338860012</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-25</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>ET897012</ChargeNumber>
                  <ExpirationDate>2027-08-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-25</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-25</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3340347100</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-25</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>5896MX</ChargeNumber>
                  <ExpirationDate>2029-02-28</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-25</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-25</TakingDate>
              <TakingTime>17:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3052520031</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-25</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>B54901</ChargeNumber>
                  <ExpirationDate>2024-05-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-25</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-25</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-26</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3338860012</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-26</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>ET897012</ChargeNumber>
                  <ExpirationDate>2027-08-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-26</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-26</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3340347100</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-26</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>5896MX</ChargeNumber>
                  <ExpirationDate>2029-02-28</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-26</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-26</TakingDate>
              <TakingTime>17:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3052520031</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-26</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>B54901</ChargeNumber>
                  <ExpirationDate>2024-05-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-26</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-26</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-27</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3338860012</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-27</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>ET897012</ChargeNumber>
                  <ExpirationDate>2027-08-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-27</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-27</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3340347100</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-27</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>5896MX</ChargeNumber>
                  <ExpirationDate>2029-02-28</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-27</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-27</TakingDate>
              <TakingTime>17:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3052520031</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-27</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>B54901</ChargeNumber>
                  <ExpirationDate>2024-05-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-27</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-27</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-28</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3338860012</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-28</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>ET897012</ChargeNumber>
                  <ExpirationDate>2027-08-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-28</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-02-28</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3340347100</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-28</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>5896MX</ChargeNumber>
                  <ExpirationDate>2029-02-28</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-28</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-28</TakingDate>
              <TakingTime>17:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3052520031</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-28</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>B54901</ChargeNumber>
                  <ExpirationDate>2024-05-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-28</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-28</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-03-01</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3338860012</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-03-01</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>ET897012</ChargeNumber>
                  <ExpirationDate>2027-08-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-03-01</TakingDate>
              <TakingTime>07:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Morning</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>2</Dosage>
              <TakingDate>2025-03-01</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>2</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3340347100</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-03-01</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>5896MX</ChargeNumber>
                  <ExpirationDate>2029-02-28</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-03-01</TakingDate>
              <TakingTime>11:30:00.0000000+01:00</TakingTime>
              <TakingColumn>Noon</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-03-01</TakingDate>
              <TakingTime>17:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Evening</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3052520031</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-03-01</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>B54901</ChargeNumber>
                  <ExpirationDate>2024-05-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-03-01</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-03-01</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
          </Medications>
        </Job>
        <Job>
          <ID>817769</ID>
          <Type>WeekA4</Type>
          <Medications>
            <Medication>
              <DrugID>3052520031</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-17</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>B54901</ChargeNumber>
                  <ExpirationDate>2024-05-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-17</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LMT5461</ChargeNumber>
                  <ExpirationDate>2026-01-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-17</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3052520031</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-18</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>B54901</ChargeNumber>
                  <ExpirationDate>2024-05-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-18</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LMT5461</ChargeNumber>
                  <ExpirationDate>2026-01-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-18</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3052520031</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-19</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>B54901</ChargeNumber>
                  <ExpirationDate>2024-05-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-19</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LMT5461</ChargeNumber>
                  <ExpirationDate>2026-01-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-19</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3052520031</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-20</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>B54901</ChargeNumber>
                  <ExpirationDate>2024-05-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-20</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-20</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3052520031</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-21</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>B54901</ChargeNumber>
                  <ExpirationDate>2024-05-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-21</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-21</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3052520031</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-23</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>B54901</ChargeNumber>
                  <ExpirationDate>2024-05-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-23</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-02-23</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3052520031</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-03-02</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>B54901</ChargeNumber>
                  <ExpirationDate>2024-05-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>**********</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-03-02</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>LM89111</ChargeNumber>
                  <ExpirationDate>2028-01-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
            <Medication>
              <DrugID>3348710500</DrugID>
              <DoctorID>1</DoctorID>
              <Dosage>1</Dosage>
              <TakingDate>2025-03-02</TakingDate>
              <TakingTime>21:00:00.0000000+01:00</TakingTime>
              <TakingColumn>Night</TakingColumn>
              <Packages>
                <Package>
                  <ChargeNumber>MXC548</ChargeNumber>
                  <ExpirationDate>2028-10-31</ExpirationDate>
                  <Quantity>1</Quantity>
                </Package>
              </Packages>
            </Medication>
          </Medications>
        </Job>
      </Jobs>
    </Patient>
  </Patients>
</BlisterOrder>