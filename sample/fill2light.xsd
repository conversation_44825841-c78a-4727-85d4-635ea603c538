    <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">

  <xs:element name="BlisterOrder">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="ID">
          <xs:simpleType>
            <xs:restriction base="xs:string">
              <xs:pattern value="bj[0-9][0-9][0-1][0-9][0-3][0-9][0-9]{5}"></xs:pattern>
            </xs:restriction>
          </xs:simpleType>
        </xs:element>
        <xs:element name="Version" type="xs:string"></xs:element>
        <xs:element name="CreateDate" type="xs:date"></xs:element>
        <xs:element name="CreateTime" type="xs:time"></xs:element>
        <xs:element name="Drugs">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Drug" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="ID" type="String30"></xs:element>
                    <xs:element name="Name" type="String50"></xs:element>
                    <xs:element name="Manufacturer" type="String50"></xs:element>
                    <xs:element name="MedicineSubstance" minOccurs="0" type="String70"></xs:element>
                    <xs:element name="ATCCode" minOccurs="0" type="String10"></xs:element>
                    <xs:element name="ATCDescription1" minOccurs="0" type="String70"></xs:element>
                    <xs:element name="ATCDescription2" minOccurs="0" type="String70"></xs:element>
                    <xs:element name="ATCDescription3" minOccurs="0" type="String70"></xs:element>
                    <xs:element name="INDCode" minOccurs="0" type="String10"></xs:element>
                    <xs:element name="INDDescription1" minOccurs="0" type="String70"></xs:element>
                    <xs:element name="INDDescription2" minOccurs="0" type="String70"></xs:element>
                    <xs:element name="INDDescription3" minOccurs="0" type="String70"></xs:element>
                    <xs:element name="Type" minOccurs="0" type="String20"></xs:element>
                    <xs:element name="InfoSonde" minOccurs="0" type="String100"></xs:element>
                    <xs:element name="TakingAdvice1" minOccurs="0" type="String100"></xs:element>
                    <xs:element name="TakingAdvice2" minOccurs="0" type="String100"></xs:element>
                    <xs:element name="TakingAdvice3" minOccurs="0" type="String100"></xs:element>
                    <xs:element name="TakingAdvice4" minOccurs="0" type="String100"></xs:element>
                    <xs:element name="Divisible" minOccurs="0">
                      <xs:simpleType>
                        <xs:restriction base="xs:string">
                          <xs:enumeration value="yes"></xs:enumeration>
                          <xs:enumeration value="no"></xs:enumeration>
                          <xs:enumeration value="undefined"></xs:enumeration>
                          <xs:enumeration value="half"></xs:enumeration>
                          <xs:enumeration value="third"></xs:enumeration>
                          <xs:enumeration value="quarter"></xs:enumeration>
                          <xs:enumeration value="all"></xs:enumeration>
                        </xs:restriction>
                      </xs:simpleType>
                    </xs:element>
                    <xs:element name="Color" minOccurs="0" type="String20"></xs:element>
                    <xs:element name="Form" minOccurs="0" type="String20"></xs:element>
                    <xs:element name="Width" minOccurs="0" type="String20"></xs:element>
                    <xs:element name="Height" minOccurs="0" type="String20"></xs:element>
                    <xs:element name="Length" minOccurs="0" type="String20"></xs:element>
                    <xs:element name="Weight" minOccurs="0" type="String20"></xs:element>
                    <xs:element name="Diameter" minOccurs="0" type="String20"></xs:element>
                    <xs:element name="FrontMarking" minOccurs="0" type="String50"></xs:element>
                    <xs:element name="RearMarking" minOccurs="0" type="String50"></xs:element>
                    <xs:element name="FotoData" minOccurs="0" type="xs:base64Binary"></xs:element>
                    <xs:element name="FotoFile" minOccurs="0" type="xs:anyURI"></xs:element>
                    <xs:element name="PackingTypes">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element name="PackingType" maxOccurs="unbounded">
                            <xs:complexType>
                              <xs:sequence>
                                <xs:element name="ID" type="String10"></xs:element>
                                <xs:element name="Size" type="xs:int"></xs:element>
                              </xs:sequence>
                            </xs:complexType>
                          </xs:element>
                        </xs:sequence>
                      </xs:complexType>
                    </xs:element>
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Doctors">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Doctor">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="ID" type="String30"></xs:element>
                    <xs:element name="Name1" type="String50"></xs:element>
                    <xs:element name="Name2" minOccurs="0" type="String50"></xs:element>
                    <xs:element name="Street" type="String50"></xs:element>
                    <xs:element name="ZipCode" type="String10"></xs:element>
                    <xs:element name="Town" type="String50"></xs:element>
                    <xs:element name="Phone" type="String50"></xs:element>
                    <xs:element name="FAX" minOccurs="0" type="String50"></xs:element>
                    <xs:element name="Email" minOccurs="0" type="String50"></xs:element>
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Pharmacy">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID" type="String10"></xs:element>
              <xs:element name="Name1" type="String50"></xs:element>
              <xs:element name="Name2" minOccurs="0" type="String50"></xs:element>
              <xs:element name="Street" type="String50"></xs:element>
              <xs:element name="ZipCode" type="String10"></xs:element>
              <xs:element name="Town" type="String50"></xs:element>
              <xs:element name="Phone" type="String50"></xs:element>
              <xs:element name="FAX" minOccurs="0" type="String50"></xs:element>
              <xs:element name="Email" minOccurs="0" type="String50"></xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Patients">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Patient" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="ID" type="String10"></xs:element>
                    <xs:element name="Title" minOccurs="0" type="String50"></xs:element>
                    <xs:element name="Name1" type="String50"></xs:element>
                    <xs:element name="Name2" type="String50"></xs:element>
                    <xs:element name="FotoData" minOccurs="0" type="xs:base64Binary"></xs:element>
                    <xs:element name="FotoFile" minOccurs="0" type="xs:anyURI"></xs:element>
                    <xs:element name="Birthday" type="xs:date"></xs:element>
                    <xs:element name="InsuranceID" minOccurs="0" type="String20"></xs:element>
                    <xs:element name="HealthInsuranceID" minOccurs="0" type="String20"></xs:element>
                    <xs:element name="HealthInsuranceName" minOccurs="0" type="String20"></xs:element>
                    <xs:choice>
                      <xs:element name="Address">
                        <xs:complexType>
                          <xs:sequence>
                            <xs:element name="Street" minOccurs="0" type="String50"></xs:element>
                            <xs:element name="ZipCode" minOccurs="0" type="String10"></xs:element>
                            <xs:element name="Town" minOccurs="0" type="String50"></xs:element>
                          </xs:sequence>
                        </xs:complexType>
                      </xs:element>
                      <xs:element name="NursingHome">
                        <xs:complexType>
                          <xs:sequence>
                            <xs:element name="Name" minOccurs="0" type="String50"></xs:element>
                            <xs:element name="Station" minOccurs="0" type="String50"></xs:element>
                            <xs:element name="Room" minOccurs="0" type="String10"></xs:element>
                          </xs:sequence>
                        </xs:complexType>
                      </xs:element>
                    </xs:choice>
                    <xs:element name="Jobs">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element name="Job" maxOccurs="unbounded">
                            <xs:complexType>
                              <xs:sequence>
                                <xs:element name="ID" type="String10"></xs:element>
                                <xs:element name="BlistercardID" type="String10" minOccurs="0"></xs:element>
                                <xs:element name="Type">
                                  <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                      <!-- Week -->
                                      <xs:enumeration value="WeekA4"></xs:enumeration>
                                      <xs:enumeration value="WeekA5"></xs:enumeration>
                                      <xs:enumeration value="WeekB4"></xs:enumeration>
                                      <xs:enumeration value="WeekB5"></xs:enumeration>
                                      <!-- Month -->
                                      <xs:enumeration value="Month"></xs:enumeration>
                                      <!-- Flex -->
                                      <xs:enumeration value="FlexA4"></xs:enumeration>
                                      <xs:enumeration value="FlexA5"></xs:enumeration>
                                      <xs:enumeration value="FlexB4"></xs:enumeration>
                                      <xs:enumeration value="FlexB5"></xs:enumeration>
                                      <!-- Day -->
                                      <xs:enumeration value="DayA"></xs:enumeration>
                                      <xs:enumeration value="DayB"></xs:enumeration>
                                    </xs:restriction>
                                  </xs:simpleType>
                                </xs:element>
                                <xs:element name="Medications">
                                  <xs:complexType>
                                    <xs:sequence>
                                      <xs:element name="Medication" maxOccurs="unbounded">
                                        <xs:complexType>
                                          <xs:sequence>
                                            <xs:element name="DrugID" type="String30"></xs:element>
                                            <xs:element name="DoctorID" type="String30"></xs:element>
                                            <xs:element name="Dosage" type="xs:decimal"></xs:element>
                                            <xs:element name="TakingDate" type="xs:date"></xs:element>
                                            <xs:element name="TakingRow" minOccurs="0" default="Undefined">
                                              <xs:simpleType>
                                                <xs:restriction base="xs:string">
                                                  <xs:enumeration value="Undefined"></xs:enumeration>
                                                  <!-- Flex -->
                                                  <xs:enumeration value="Row1"></xs:enumeration>
                                                  <xs:enumeration value="Row2"></xs:enumeration>
                                                  <xs:enumeration value="Row3"></xs:enumeration>
                                                  <xs:enumeration value="Row4"></xs:enumeration>
                                                  <xs:enumeration value="Row5"></xs:enumeration>
                                                  <xs:enumeration value="Row6"></xs:enumeration>
                                                  <xs:enumeration value="Row7"></xs:enumeration>
                                                </xs:restriction>
                                              </xs:simpleType>
                                            </xs:element>
                                            <xs:element name="TakingTime" type="xs:time"></xs:element>
                                            <xs:element name="TakingColumn" minOccurs="0" default="Undefined">
                                              <xs:simpleType>
                                                <xs:restriction base="xs:string">
                                                  <xs:enumeration value="Undefined"></xs:enumeration>
                                                  <!-- Day, Week -->
                                                  <xs:enumeration value="Sober"></xs:enumeration>
                                                  <xs:enumeration value="Morning"></xs:enumeration>
                                                  <xs:enumeration value="Noon"></xs:enumeration>
                                                  <xs:enumeration value="Evening"></xs:enumeration>
                                                  <xs:enumeration value="Night"></xs:enumeration>
                                                  <!-- Flex -->
                                                  <xs:enumeration value="Column1"></xs:enumeration>
                                                  <xs:enumeration value="Column2"></xs:enumeration>
                                                  <xs:enumeration value="Column3"></xs:enumeration>
                                                  <xs:enumeration value="Column4"></xs:enumeration>
                                                  <xs:enumeration value="Column5"></xs:enumeration>
                                                </xs:restriction>
                                              </xs:simpleType>
                                            </xs:element>
                                            <xs:element name="Information" type="String50" minOccurs="0"></xs:element>
                                            <xs:element name="Packages">
                                              <xs:complexType>
                                                <xs:sequence>
                                                  <xs:element name="Package" maxOccurs="unbounded">
                                                    <xs:complexType>
                                                      <xs:sequence>
                                                        <xs:element name="ChargeNumber" type="String50" minOccurs="0"></xs:element>
                                                        <xs:element name="ExpirationDate" type="xs:date" minOccurs="0"></xs:element>
                                                        <xs:element name="Quantity" type="xs:decimal"></xs:element>
                                                      </xs:sequence>
                                                    </xs:complexType>
                                                  </xs:element>
                                                </xs:sequence>
                                              </xs:complexType>
                                            </xs:element>
                                          </xs:sequence>
                                        </xs:complexType>
                                      </xs:element>
                                    </xs:sequence>
                                  </xs:complexType>
                                </xs:element>
                              </xs:sequence>
                            </xs:complexType>
                          </xs:element>
                        </xs:sequence>
                      </xs:complexType>
                    </xs:element>
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
 
    <xs:key name="DrugIDKey">
      <xs:selector xpath="./Drugs/Drug"></xs:selector>
      <xs:field xpath="ID"></xs:field>
    </xs:key>
 
    <xs:keyref name="DrugIDKeyRef" refer="DrugIDKey">
      <xs:selector xpath="./Patients/Patient/Jobs/Job/Medications/Medication"></xs:selector>
      <xs:field xpath="DrugID"></xs:field>
    </xs:keyref>

    <xs:key name="DoctorIDKey">
      <xs:selector xpath="./Doctors/Doctor"></xs:selector>
      <xs:field xpath="ID"></xs:field>
    </xs:key>

    <xs:keyref name="DoctorIDKeyRef" refer="DoctorIDKey">
      <xs:selector xpath="./Patients/Patient/Jobs/Job/Medications/Medication"></xs:selector>
      <xs:field xpath="DoctorID"></xs:field>
    </xs:keyref>

  </xs:element>

  <xs:simpleType name="String10">
    <xs:restriction base="xs:string">
      <xs:maxLength value="10"></xs:maxLength>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="String20">
    <xs:restriction base="xs:string">
      <xs:maxLength value="20"></xs:maxLength>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="String30">
    <xs:restriction base="xs:string">
      <xs:maxLength value="30"></xs:maxLength>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="String50">
    <xs:restriction base="xs:string">
      <xs:maxLength value="50"></xs:maxLength>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="String70">
    <xs:restriction base="xs:string">
      <xs:maxLength value="70"></xs:maxLength>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="String100">
    <xs:restriction base="xs:string">
      <xs:maxLength value="100"></xs:maxLength>
    </xs:restriction>
  </xs:simpleType>

</xs:schema>
