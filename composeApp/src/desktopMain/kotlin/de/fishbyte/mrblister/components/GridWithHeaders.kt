package de.fishbyte.mrblister.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.BasicText
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeFormatter

@Composable
fun <T> GridWithHeaders(
    columnHeaders: List<String>, // Column headers
    rowHeaders: List<String>,    // Row headers
    gridContent: List<List<T>>,
    rowHeights: List<Dp> = emptyList(),
    columnWidths: List<Dp> = emptyList(),
    modifier: Modifier = Modifier,
    item: @Composable (T) -> Unit
) {
    Column(/*modifier = Modifier.fillMaxSize()*/ ) {

        // Display column headers
        Row(
            modifier = Modifier
                /*.fillMaxWidth()*/
                .height(40.dp)
        ) {
            if( rowHeights.isNotEmpty() ) {
                Spacer(modifier = Modifier.weight(1f))
            }
            Spacer(modifier = Modifier.width(80.dp)) // Top-left corner spacer for alignment

            columnHeaders.forEachIndexed { index, columnHeader ->
            Box(
                if (columnWidths.isNotEmpty()) {
                    Modifier
                        .width(columnWidths[index])
                        .fillMaxHeight()
                        .padding(2.dp)
                        .background(Color.Gray)
                } else {
                    Modifier
                        .weight(1f)
                        .fillMaxHeight()
                        .padding(2.dp)
                        .background(Color.Gray)
                },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = columnHeader,
                        fontSize = androidx.compose.material3.MaterialTheme.typography.headlineSmall.fontSize,
                        style = MaterialTheme.typography.body1.copy(
                            color = Color.White,
                            fontSize = 16.sp
                        ),
                        textAlign = TextAlign.Center
                    )
                }
            }
        }

        for( row in rowHeaders.indices) {

            Row( modifier = if (rowHeights.isNotEmpty()) {
                Modifier
                    .fillMaxWidth()
                    .height(rowHeights[row])
            } else {
                Modifier
                    .fillMaxWidth()
                    .weight(1F)
            }) {
                if( rowHeights.isNotEmpty() ) {
                    Spacer(modifier = Modifier.weight(1f))
                }

                rowHeaders[row].let { rowHeader ->
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .width(80.dp)
                            .padding(2.dp)
                            .background(Color.Gray),
                        contentAlignment = Alignment.Center
                    ) {
                        BasicText(
                            modifier = Modifier.rotate(-90f),
                            text = rowHeader?.format(DateTimeFormatter.ISO_LOCAL_TIME) ?: "",
                            style = MaterialTheme.typography.body2.copy(
                                color = Color.White,
                                fontSize = androidx.compose.material3.MaterialTheme.typography.headlineSmall.fontSize
                            )
                        )
                    }
                }

                for( col in columnHeaders.indices) {
                    Box(
                        modifier = if( rowHeights.isNotEmpty() ) {
                            Modifier
                                .size(columnWidths[col], rowHeights[row])
                                .fillMaxHeight()
                                .padding(2.dp)
                                .background(Color.LightGray)
                        } else {
                            Modifier
                                .weight(1f)
                                .fillMaxHeight()
                                .padding(2.dp)
                                .background(Color.LightGray)
                        },
                        contentAlignment = Alignment.TopStart
                    ) {
                        item(gridContent[row][col])
                    }
                }
            }
        }
    }
}
