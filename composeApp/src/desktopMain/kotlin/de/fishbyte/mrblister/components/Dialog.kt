import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.width
import androidx.compose.material.Surface
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.Settings
import org.koin.compose.getKoin
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.MaterialTheme
import androidx.compose.foundation.shape.RoundedCornerShape

@Composable
fun Dialog(
    title: String,
    message: String,
    onCloseMessage: String = "Abbrechen",
    onClose: ((confirmed: Boolean) -> Unit)? = null,
    onConfirmMessage: String = "Bestätigen",
    onConfirm: () -> Unit
) {
    Dialog(title, {
        Text(
            text = message,
            style = MaterialTheme.typography.bodyMedium
        )
    }, onCloseMessage, onClose, onConfirmMessage, onConfirm )
}


@Composable
fun Dialog(
    title: String,
    content: @Composable (() -> Unit),
    onCloseMessage: String? = null,
    onClose: ((confirmed: Boolean) -> Unit)? = null,
    onConfirmMessage: String? = null,
    onConfirm: (() -> Unit)? = null
) {
    val settings = getKoin().get<Settings>()
    val multiMonitor = settings.multiMonitor

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.5f))
            .padding(64.dp),
        contentAlignment = if( multiMonitor ) Alignment.Center else Alignment.CenterStart
    ) {
        Row() {
            Surface(
                modifier = if( multiMonitor ) Modifier.width(800.dp) else Modifier.weight(1f),
                shape = RoundedCornerShape(8.dp),
                elevation = 8.dp
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.headlineSmall
                    )
                    Box() {
                        content()
                    }
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        if( onClose != null ) {
                            Button(
                                onClick = {
                                    onClose(false)
                                },
                                colors = ButtonDefaults.outlinedButtonColors(),
                                modifier = Modifier.padding(end = 8.dp)
                            ) {
                                Text(onCloseMessage ?: "Abbrechen")
                            }
                        }

                        if( onConfirm != null ) {
                            Button(onClick = {
                                if (onClose != null) {
                                    onClose(true)
                                }
                                onConfirm()
                            }) {
                                Text(onConfirmMessage ?: "Bestätigen")
                            }
                        }
                    }
                }
            }
            if( !multiMonitor ) {
                Box( modifier = Modifier.width(settings.blisterWidth.dp))
            }
        }
    }
}
