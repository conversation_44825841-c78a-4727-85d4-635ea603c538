package de.fishbyte.mrblister.components

import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.*
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.unit.dp

class Components {
    companion object {
        @Composable
        fun primaryButton(
            text: String,
            onClick: () -> Unit,
            modifier: Modifier = Modifier
        ) {
            val requester = remember { FocusRequester() }

            Button(
                onClick = onClick,
                modifier = modifier
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(text)
                }
            }
        }

        @Composable
        fun secondaryButton(
            text: String,
            onClick: () -> Unit,
            modifier: Modifier = Modifier // Optional modifier parameter
        ) {
            val requester = remember { FocusRequester() }

            Button(
                onClick = onClick,
                colors = ButtonDefaults.outlinedButtonColors(),
                modifier = modifier
            ) {

                Text(text)
            }
        }
    }}