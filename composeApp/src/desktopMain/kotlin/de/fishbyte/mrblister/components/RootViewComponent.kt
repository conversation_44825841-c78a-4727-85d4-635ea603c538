package de.fishbyte.mrblister.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.Settings
import org.koin.compose.getKoin
import org.koin.mp.KoinPlatform

@Composable
fun RootViewComponent( footer: @Composable (RowScope.() -> Unit)? = null, content: @Composable BoxScope.() -> Unit ) : Unit {

    val settings = KoinPlatform.getKoin().get<Settings>()
    val blisterWidth = settings.blisterWidth.dp
    val multiMonitor = true // settings.multiMonitor

    Column( modifier = Modifier.fillMaxSize()) {
        Box( Modifier.fillMaxWidth().weight(1f).padding(16.dp)) {
            content()
        }

        footer?.let { f ->
            Row(
                modifier = Modifier.fillMaxWidth().height(96.dp).padding(16.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.End) {
                f()

                if( multiMonitor ) {
                    Box(
                        modifier = Modifier
                            .size(blisterWidth)
                            /* .border(1.dp, Color.Black)
                            .background(Color.LightGray) */)
                }
            }
        }
    }
}