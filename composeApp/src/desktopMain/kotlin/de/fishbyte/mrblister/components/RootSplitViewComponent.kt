package de.fishbyte.mrblister.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.Settings
import org.koin.compose.getKoin
import org.koin.mp.KoinPlatform

@Composable
fun RootSplitViewComponent( footer: @Composable (RowScope.() -> Unit)? = null, content: @Composable ColumnScope.() -> Unit, blisterContent: @Composable ColumnScope.() -> Unit ) : Unit {

    val settings = KoinPlatform.getKoin().get<Settings>()
    val blisterWidth = settings.blisterWidth.dp
    val multiMonitor = true // settings.multiMonitor

    Row(modifier = Modifier.fillMaxSize()) {

        Column(modifier = Modifier.weight(1f).padding(16.dp)) {
            Column(Modifier.weight(1f)) {
                content()
            }

            footer?.let { f ->
                Spacer(modifier = Modifier.height(16.dp))
                Row(
                    modifier = Modifier.fillMaxWidth().height(48.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start
                ) {
                    f()
                }
            }
        }

        //Spacer(modifier = Modifier.width(16.dp))

        Column(modifier = Modifier.width(blisterWidth).fillMaxHeight().background(Color.Black)) {
            blisterContent()
        }
    }
}