package de.fishbyte.mrblister.model

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.MapperFeature
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.dataformat.xml.JacksonXmlModule
import com.fasterxml.jackson.dataformat.xml.XmlMapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import java.io.File

@JacksonXmlRootElement(localName = "Blister")
data class BlisterConfiguration(
    @JacksonXmlProperty(localName = "Name") val name: String,
    @JacksonXmlProperty(localName = "ColumnWidth") @JacksonXmlElementWrapper(useWrapping = false) val columnWidth: List<Float>,
    @JacksonXmlProperty(localName = "RowHeight") @JacksonXmlElementWrapper(useWrapping = false) val rowHeight: List<Float>,
    @JacksonXmlProperty(localName = "MarginX") val marginX: Float,
    @JacksonXmlProperty(localName = "MarginY") val marginY: Float,
    @JacksonXmlProperty(localName = "flipTime") val flipTime: Boolean?,
    @JacksonXmlProperty(localName = "flipDays") val flipDays: Boolean?,
    @JacksonXmlProperty(localName = "stations") @JacksonXmlElementWrapper(useWrapping = false) val stations: List<String> = emptyList()
)

@JacksonXmlRootElement(localName = "Configurations")
data class BlisterConfigurationsXMLImpl(
    @JacksonXmlElementWrapper(useWrapping = false)
    @JacksonXmlProperty(localName = "Blister")
    val blister: List<BlisterConfiguration>
)

fun loadBlisterConfig(path: String) : List<BlisterConfiguration> {
    val xmlMapper =
        XmlMapper(JacksonXmlModule())
            .registerModule(JavaTimeModule())
            .findAndRegisterModules()
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS) // to parse the dates as LocalDate, else parsing error
            .enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES)
            .enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT)
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
    val configFile = File(path,"blister.xml")

    val settings = if( configFile.exists() ) {
        xmlMapper.readValue(configFile, BlisterConfigurationsXMLImpl::class.java)
    } else {
        null
    }

    if( settings == null ) {
        val config1 = BlisterConfiguration("WeekA4", listOf(180f, 180f, 180f, 180f, 180f, 180f, 180f), listOf(190f, 190f, 190f, 160f), 62f, 62f, false, false, emptyList())
        val config2 = BlisterConfiguration("WeekA5", listOf(180f, 180f, 180f, 180f, 180f, 180f, 180f), listOf(160f, 190f, 190f, 190f, 160f), 62f, 62f, false, false, emptyList())
        return listOf(config1, config2)
    } else {
        return settings.blister
    }
}

fun saveBlisterConfig(path: String, config: List<BlisterConfiguration> ) {

    val xmlMapper =
        XmlMapper(JacksonXmlModule())
            .registerModule(JavaTimeModule())
            .findAndRegisterModules()
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS) // to parse the dates as LocalDate, else parsing error
            .enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES)
            .enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT)
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)

    xmlMapper.writeValue(File(path, "blister.xml"), BlisterConfigurationsXMLImpl( config) )
}
