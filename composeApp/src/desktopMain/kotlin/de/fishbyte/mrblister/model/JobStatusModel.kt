package de.fishbyte.mrblister.model

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.dataformat.xml.XmlMapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import de.fishbyte.mrblister.app.AppViewModel
import de.fishbyte.mrblister.app.Settings
import java.io.File

enum class JobStatus {
    Pending,
    InProgress,
    Completed,
    Cancelled,
    Paused
}

@JacksonXmlRootElement(localName = "jobStatus")
data class JobStatusModel(
    @JsonProperty("status")
    @JacksonXmlProperty(localName = "status")
    var status: JobStatus = JobStatus.Pending,

    @JsonProperty("id")
    @JacksonXmlProperty(localName = "id")
    var localID: String,

    @JsonProperty("medications")
    @JacksonXmlProperty(localName = "medications")
    var medications: List<MedicationStatusModel> = emptyList()
)

fun JobStatusModel.getMedicationStatus(drugID: String): MedicationStatusModel {
    var status = medications.find { it.drugId == drugID } as MedicationStatusModel?
    if( status == null ) {
        status = MedicationStatusModel()
        status.status = MedicationStatus.Pending
        status.drugId = drugID
        medications = medications.plus(status)
    }
    return status
}

/** returns true if there is no medication that is not in status Pending */
fun JobStatusModel.isPending() : Boolean {
    return medications.none { it.status != MedicationStatus.Pending }
}

fun AppViewModel.write( status: JobStatusModel) {


    val objectMapper = ObjectMapper().registerKotlinModule()
    objectMapper.registerModule(JavaTimeModule())
    objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL)

    val targetDirectory = jobStatusStoragePath()
    targetDirectory.mkdirs()

    val configFile = File(targetDirectory, "job_${status.localID}.status.json")
    objectMapper.writeValue(configFile, status)
}

fun AppViewModel.loadJobStatus() {
    val objectMapper = ObjectMapper().apply {
        registerKotlinModule()
        registerModule(JavaTimeModule())
    }
    val baseDir = jobStatusStoragePath()

    val status = ( baseDir.listFiles { file ->
        file.name.startsWith("job_") && file.name.endsWith(".status.json")
    }?.mapNotNull { file ->
        try {
            objectMapper.readValue(file, JobStatusModel::class.java)
        } catch (e: Exception) {
            // TODO: Log this
            e.printStackTrace()
            null
        }
    } ?: emptyList() ) as List<JobStatusModel>

    updateJobStatus(status)
}