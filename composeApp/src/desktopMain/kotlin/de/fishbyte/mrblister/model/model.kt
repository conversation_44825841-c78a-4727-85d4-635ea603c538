package de.fishbyte.mrblister.model

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.*
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.fasterxml.jackson.dataformat.xml.XmlMapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import de.fishbyte.mrblister.app.AppViewModel
import de.fishbyte.mrblister.services.SearchService
import org.jetbrains.skia.defaultLanguageTag
import java.io.File
import java.math.BigDecimal
import java.nio.file.Path
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.time.temporal.TemporalAdjusters


class CustomLocalDateSerializer : JsonSerializer<LocalDate>() {
    private val formatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")


    override fun serialize(value: LocalDate, gen: JsonGenerator, serializers: SerializerProvider) {
        gen.writeString(value.format(formatter))
    }
}


class CustomLocalDateDeserializer : JsonDeserializer<LocalDate>() {
    private val formatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")

    override fun deserialize(parser: JsonParser, context: DeserializationContext): LocalDate {
        return LocalDate.parse(parser.text, formatter)
    }
}
class CustomLocalTimeSerializer : JsonSerializer<LocalTime>() {
    private val formatter: DateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss")

    override fun serialize(value: LocalTime, gen: JsonGenerator, serializers: SerializerProvider) {
        gen.writeString(value.atDate(LocalDate.now()).format(formatter))
    }
}
class CustomLocalTimeDeserializer : JsonDeserializer<LocalTime>() {
    private val formatter: DateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss")

    override fun deserialize(parser: JsonParser, context: DeserializationContext): LocalTime {

        var text = parser.text
        val index = text.indexOf('.')
        if( index != -1 ) {
            text = text.substring(0,index)
        }
        val offsetDateTime = LocalTime.parse(text, formatter)
        return offsetDateTime
    }
}

data class BlisterOrder(
    @JacksonXmlProperty(localName = "_ID") val localID: String?,
    @JacksonXmlProperty(localName = "ID") val id: String,
    @JacksonXmlProperty(localName = "Version") val version: String,
    @JsonSerialize(using = CustomLocalDateSerializer::class)
    @JsonDeserialize(using = CustomLocalDateDeserializer::class)
    @JacksonXmlProperty(localName = "CreateDate") val createDate: LocalDate,
    @JsonSerialize(using = CustomLocalTimeSerializer::class)
    @JsonDeserialize(using = CustomLocalTimeDeserializer::class)
    @JacksonXmlProperty(localName = "CreateTime") val createTime: LocalTime,
    @JacksonXmlProperty(localName = "Drugs") val drugs: Drugs,
    @JacksonXmlProperty(localName = "Doctors") val doctors: Doctors,
    @JacksonXmlProperty(localName = "Pharmacy") val pharmacy: Pharmacy,
    @JacksonXmlProperty(localName = "Patients") val patients: Patients
)

data class Drugs(
    @JacksonXmlElementWrapper(useWrapping = false)
    @JacksonXmlProperty(localName = "Drug") val drugList: List<Drug>
)

data class Drug(
    @JacksonXmlProperty(localName = "ID")
    @JsonProperty("id")
    val id: String,

    @JacksonXmlProperty(localName = "Name")
    @JsonProperty("name")
    val name: String,

    @JacksonXmlProperty(localName = "Manufacturer")
    @JsonProperty("manufacturer")
    val manufacturer: String,

    @JacksonXmlProperty(localName = "MedicineSubstance")
    @JsonProperty("medicineSubstance")
    val medicineSubstance: String?,

    @JacksonXmlProperty(localName = "ATCCode")
    @JsonProperty("ATCCode")
    val atcCode: String?,

    @JacksonXmlProperty(localName = "ATCDescription1")
    @JsonProperty("ATCDescription1")
    val atcDescription1: String?,

    @JacksonXmlProperty(localName = "ATCDescription2")
    @JsonProperty("ATCDescription2")
    val atcDescription2: String?,

    @JacksonXmlProperty(localName = "ATCDescription3")
    @JsonProperty("ATCDescription3")
    val atcDescription3: String?,

    @JacksonXmlProperty(localName = "INDCode")
    @JsonProperty("INDCode")
    val indCode: String?,

    @JacksonXmlProperty(localName = "INDDescription1")
    @JsonProperty("INDDescription1")
    val indDescription1: String?,

    @JacksonXmlProperty(localName = "INDDescription2")
    @JsonProperty("INDDescription2")
    val indDescription2: String?,

    @JacksonXmlProperty(localName = "INDDescription3")
    @JsonProperty("INDDescription3")
    val indDescription3: String?,

    @JacksonXmlProperty(localName = "Type")
    @JsonProperty("type")
    val type: String?,

    @JacksonXmlProperty(localName = "InfoSonde")
    @JsonProperty("infoSonde")
    val infoSonde: String?,

    @JacksonXmlProperty(localName = "TakingAdvice1")
    @JsonProperty("takingAdvice1")
    val takingAdvice1: String?,

    @JacksonXmlProperty(localName = "TakingAdvice2")
    @JsonProperty("takingAdvice2")
    val takingAdvice2: String?,

    @JacksonXmlProperty(localName = "TakingAdvice3")
    @JsonProperty("takingAdvice3")
    val takingAdvice3: String?,

    @JacksonXmlProperty(localName = "TakingAdvice4")
    @JsonProperty("takingAdvice4")
    val takingAdvice4: String?,

    @JacksonXmlProperty(localName = "Divisible")
    @JsonProperty("divisible")
    val divisible: String?,

    @JacksonXmlProperty(localName = "Color")
    @JsonProperty("color")
    val color: String?,

    @JacksonXmlProperty(localName = "Form")
    @JsonProperty("form")
    val form: String?,

    @JacksonXmlProperty(localName = "Width")
    @JsonProperty("width")
    val width: String?,

    @JacksonXmlProperty(localName = "Height")
    @JsonProperty("height")
    val height: String?,

    @JacksonXmlProperty(localName = "Length")
    @JsonProperty("length")
    val length: String?,

    @JacksonXmlProperty(localName = "Weight")
    @JsonProperty("weight")
    val weight: String?,

    @JacksonXmlProperty(localName = "Diameter")
    @JsonProperty("diameter")
    val diameter: String?,

    @JacksonXmlProperty(localName = "FrontMarking")
    @JsonProperty("frontMarking")
    val frontMarking: String?,

    @JacksonXmlProperty(localName = "RearMarking")
    @JsonProperty("rearMarking")
    val rearMarking: String?,

    @JacksonXmlProperty(localName = "FotoData")
    @JsonProperty("fotoData")
    val fotoData: String?,

    @JacksonXmlProperty(localName = "FotoFile")
    @JsonProperty("fotoFile")
    val fotoFile: String?,

    @JacksonXmlProperty(localName = "PackingTypes")
    @JsonProperty("packingTypes")
    val packingTypes: PackingTypes?
)
data class PackingTypes(
    @JacksonXmlElementWrapper(useWrapping = false)
    @JacksonXmlProperty(localName = "PackingType")
    @JsonProperty("packingType")
    val packingTypeList: List<PackingType>
)

data class PackingType(
    @JacksonXmlProperty(localName = "ID")
    @JsonProperty("id")
    val id: String,
    @JacksonXmlProperty(localName = "Size")
    @JsonProperty("size")
    val size: Int
)

data class Doctors(
    @JacksonXmlElementWrapper(useWrapping = false)
    @JacksonXmlProperty(localName = "Doctor") val doctorList: List<Doctor>
)

data class Doctor(
    @JacksonXmlProperty(localName = "ID")
    @JsonProperty("id")
    val id: String,

    @JacksonXmlProperty(localName = "Name1")
    @JsonProperty("name1")
    val name1: String,

    @JacksonXmlProperty(localName = "Name2")
    @JsonProperty("name2")
    val name2: String?,

    @JacksonXmlProperty(localName = "Street")
    @JsonProperty("street")
    val street: String,

    @JacksonXmlProperty(localName = "ZipCode")
    @JsonProperty("zipCode")
    val zipCode: String,

    @JacksonXmlProperty(localName = "Town")
    @JsonProperty("town")
    val town: String,

    @JacksonXmlProperty(localName = "Phone")
    @JsonProperty("phone")
    val phone: String,

    @JacksonXmlProperty(localName = "FAX")
    @JsonProperty("fax")
    val fax: String?,

    @JacksonXmlProperty(localName = "Email")
    @JsonProperty("email")
    val email: String?
)

data class Pharmacy(
    @JacksonXmlProperty(localName = "ID")
    @JsonProperty("id")
    val id: String,

    @JacksonXmlProperty(localName = "Name1")
    @JsonProperty("name1")
    val name1: String,

    @JacksonXmlProperty(localName = "Name2")
    @JsonProperty("name2")
    val name2: String?,

    @JacksonXmlProperty(localName = "Street")
    @JsonProperty("street")
    val street: String,

    @JacksonXmlProperty(localName = "ZipCode")
    @JsonProperty("zipCode")
    val zipCode: String,

    @JacksonXmlProperty(localName = "Town")
    @JsonProperty("town")
    val town: String,

    @JacksonXmlProperty(localName = "Phone")
    @JsonProperty("phone")
    val phone: String,

    @JacksonXmlProperty(localName = "FAX")
    @JsonProperty("fax")
    val fax: String?,

    @JacksonXmlProperty(localName = "Email")
    @JsonProperty("email")
    val email: String?
)

data class Patients(
    @JacksonXmlElementWrapper(useWrapping = false)
    @JacksonXmlProperty(localName = "Patient") val patientList: List<Patient>
)

data class Patient(
    @JacksonXmlProperty(localName = "ID") val id: String,
    @JacksonXmlProperty(localName = "Title") val title: String?,
    @JacksonXmlProperty(localName = "Name1") val name1: String,
    @JacksonXmlProperty(localName = "Name2") val name2: String,
    @JacksonXmlProperty(localName = "FotoData") val fotoData: String?, // Base64-encoded data
    @JacksonXmlProperty(localName = "FotoFile") val fotoFile: String?, // URI
    @JsonSerialize(using = CustomLocalDateSerializer::class)
    @JsonDeserialize(using = CustomLocalDateDeserializer::class)
    @JacksonXmlProperty(localName = "Birthday") val birthday: LocalDate,
    @JacksonXmlProperty(localName = "InsuranceID") val insuranceID: String?,
    @JacksonXmlProperty(localName = "HealthInsuranceID") val healthInsuranceID: String?,
    @JacksonXmlProperty(localName = "HealthInsuranceName") val healthInsuranceName: String?,
    @JacksonXmlProperty(localName = "Address") val address: Address?,
    @JacksonXmlProperty(localName = "NursingHome") val nursingHome: NursingHome?,
    @JacksonXmlProperty(localName = "Jobs") val jobs: Jobs
)

data class Address(
    @JacksonXmlProperty(localName = "Street")
    @JsonProperty("street")
    val street: String?,

    @JacksonXmlProperty(localName = "ZipCode")
    @JsonProperty("zipCode")
    val zipCode: String?,

    @JacksonXmlProperty(localName = "Town")
    @JsonProperty("town")
    val town: String?
)

data class NursingHome(
    @JacksonXmlProperty(localName = "Name")
    @JsonProperty("name")
    val name: String?,

    @JacksonXmlProperty(localName = "Station")
    @JsonProperty("station")
    val station: String?,

    @JacksonXmlProperty(localName = "Room")
    @JsonProperty("room")
    val room: String?
)

/**
 * Extension function that works on nullable NursingHome objects.
 * Returns null if the NursingHome is null, otherwise returns a concatenation of name and station.
 * If the combined name contains a ">" character, everything after and including the ">" is removed.
 */
fun NursingHome?.getNameAndStation(): String? {
    if (this == null) return null

    val nameStr = this.name ?: ""
    val stationStr = this.station ?: ""

    val result = when {
        nameStr.isBlank() && stationStr.isBlank() -> null
        nameStr.isBlank() -> stationStr
        stationStr.isBlank() -> nameStr
        else -> "$nameStr - $stationStr"
    }

    // Remove everything after and including ">" if it exists
    return result?.let {
        val indexOfGreaterThan = it.indexOf('>')
        if (indexOfGreaterThan != -1) {
            it.substring(0, indexOfGreaterThan).trim()
        } else {
            it
        }
    }
}

fun NursingHome?.getStation(): String? {
    if (this == null) return null

    // Remove everything after and including ">" if it exists
    return this.station?.let {
        val indexOfGreaterThan = it.indexOf('>')
        if (indexOfGreaterThan != -1) {
            it.substring(0, indexOfGreaterThan).trim()
        } else {
            it
        }
    }
}

data class Jobs(
    @JacksonXmlElementWrapper(useWrapping = false)
    @JacksonXmlProperty(localName = "Job") val jobList: List<Job>
)

data class Job(
    @JacksonXmlProperty(localName = "ID") val id: String,
    @JacksonXmlProperty(localName = "BlistercardID") val blistercardID: String?,
    @JacksonXmlProperty(localName = "Type") val type: String,
    @JacksonXmlProperty(localName = "Medications") val medications: Medications,
    @JacksonXmlProperty(localName = "index") val index: Int?,
    @JacksonXmlProperty(localName = "indexOf") val indexOf: Int?

    ) {
    @JsonIgnore
    fun getStartDate(): LocalDate? {
        val firstDate = medications.medicationList.minByOrNull { it.takingDate }?.takingDate
        return firstDate?.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
    }

    @JsonIgnore
    fun getEarliestTime(): LocalTime? {
        return medications.medicationList.minByOrNull { it.takingTime }?.takingTime
    }
}

data class Medications(
    @JacksonXmlElementWrapper(useWrapping = false)
    @JacksonXmlProperty(localName = "Medication") val medicationList: List<Medication>
)

data class Medication(
    @JacksonXmlProperty(localName = "DrugID")
    @JsonProperty("drugID")
    val drugID: String,

    @JacksonXmlProperty(localName = "DoctorID")
    @JsonProperty("doctorID")
    val doctorID: String,

    @JacksonXmlProperty(localName = "Dosage")
    @JsonProperty("dosage")
    val dosage: Double,

    @JsonSerialize(using = CustomLocalDateSerializer::class)
    @JsonDeserialize(using = CustomLocalDateDeserializer::class)
    @JacksonXmlProperty(localName = "TakingDate")
    @JsonProperty("takingDate")
    val takingDate: LocalDate,

    @JacksonXmlProperty(localName = "TakingRow")
    @JsonProperty("takingRow")
    val takingRow: String? = "Undefined",

    @JsonSerialize(using = CustomLocalTimeSerializer::class)
    @JsonDeserialize(using = CustomLocalTimeDeserializer::class)
    @JacksonXmlProperty(localName = "TakingTime")
    @JsonProperty("takingTime")
    val takingTime: LocalTime,

    @JacksonXmlProperty(localName = "TakingColumn")
    @JsonProperty("takingColumn")
    val takingColumn: String? = "Undefined",

    @JacksonXmlProperty(localName = "Information")
    @JsonProperty("information")
    val information: String?,

    @JacksonXmlProperty(localName = "Packages")
    @JsonProperty("packages")
    val packages: Packages
)
data class Packages(
    @JacksonXmlElementWrapper(useWrapping = false)
    @JacksonXmlProperty(localName = "Package")
    @JsonProperty("package")
    val packageList: List<Package>
)

data class Package(
    @JacksonXmlProperty(localName = "ChargeNumber")
    @JsonProperty("chargeNumber")
    val chargeNumber: String?,

    @JacksonXmlProperty(localName = "ExpirationDate")
    @JsonProperty("expirationDate")
    val expirationDate: LocalDate?,

    @JacksonXmlProperty(localName = "Quantity")
    @JsonProperty("quantity")
    val quantity: BigDecimal
)

fun loadBlisterOrder( path: String ) : BlisterOrder {
    // Create an XmlMapper instance
    val xmlMapper = XmlMapper()
    xmlMapper.registerModule(JavaTimeModule())

    // Read and parse the XML file
    val blisterOrder: BlisterOrder = xmlMapper.readValue(File(path), BlisterOrder::class.java)

    return blisterOrder
}

fun loadBlisterJob( path: String ) : BlisterOrder {
    // Create an XmlMapper instance
    val objectMapper = ObjectMapper()
    objectMapper.registerModule(JavaTimeModule())

    // Read and parse the XML file
    val blisterOrder: BlisterOrder = objectMapper.readValue(File(path), BlisterOrder::class.java)

    return blisterOrder
}

fun BlisterOrder.patientByID( patientID: String ) : Patient?
{
    return patients.patientList.stream().filter { it.id == patientID }.findFirst().orElse(null)
}

fun Patient.jobByID( jobID: String ) : Job?
{
    return this.jobs.jobList.stream().filter { it.id == jobID }.findFirst().orElse(null)
}

fun AppViewModel.archive(job: BlisterJob, searchService: SearchService) {

    try {
        val jobStatusStoragePath = jobStatusStoragePath()
        val blisterStoragePath = blisterStoragePath()
        val imageStoragePath = imageStoragePath()

        val archiveDirectory = archivePath()

        val date = LocalDate.now()

        val weekOfMonth = ((date.dayOfMonth - 1) / 7 + 1).toString()
        val monthValue = date.monthValue.toString().padStart(2, '0')

        val targetDirectory = Path.of(
            archiveDirectory.path,
            date.year.toString(),
            "${monthValue}-${weekOfMonth}"
        ).toFile()

        targetDirectory.mkdirs()

        searchService.addDocument( job, LocalDateTime.now(), targetDirectory.path )

        // Move blister file
        val fileName = job.fileName()
        val sourceBlisterFile = File("${blisterStoragePath}/${fileName}")
        val targetBlisterFile = File("${targetDirectory.path}/${fileName}")
        sourceBlisterFile.copyTo(targetBlisterFile, overwrite = true)
        sourceBlisterFile.delete()

        // Move job status file
        val statusFileName = "job_${job.localID}.status.json"
        val sourceStatusFile = File("${jobStatusStoragePath}/${statusFileName}")
        val targetStatusFile = File("${targetDirectory.path}/${statusFileName}")
        try {
            sourceStatusFile.copyTo(targetStatusFile, overwrite = true)
            sourceStatusFile.delete()
        } catch (e: Exception) {
            e.printStackTrace()
            logger.error(e.toString())
        }

        // Move image files
        val imagePattern = "image_${job.localID}*"
        imageStoragePath.listFiles { file ->
            file.name.matches(Regex(imagePattern.replace("*", ".*")))
        }?.forEach { file ->
            val targetFile = File("${targetDirectory.path}/${file.name}")
            file.copyTo(targetFile, overwrite = true)
            file.delete()
        }

        removeJob(job)

    } catch (e: Exception) {
        e.printStackTrace()
        logger.error(e.toString())
    }
}

fun AppViewModel.remove(job: BlisterJob) {

    try {
        val jobStatusStoragePath = jobStatusStoragePath()
        val blisterStoragePath = blisterStoragePath()
        val imageStoragePath = imageStoragePath()

        val date = LocalDate.now()

        val weekOfMonth = ((date.dayOfMonth - 1) / 7 + 1).toString()
        val monthValue = date.monthValue.toString().padStart(2, '0')

        // Remove blister file
        val fileName = job.fileName()
        val sourceBlisterFile = File("${blisterStoragePath}/${fileName}")
        sourceBlisterFile.delete()

        // Remove job status file
        val statusFileName = "job_${job.localID}.status.json"
        val sourceStatusFile = File("${jobStatusStoragePath}/${statusFileName}")
        try {
            sourceStatusFile.delete()
        } catch (e: Exception) {
            e.printStackTrace()
            logger.error(e.toString())
        }

        // delete image files
        val imagePattern = "image_${job.localID}*"
        imageStoragePath.listFiles { file ->
            file.name.matches(Regex(imagePattern.replace("*", ".*")))
        }?.forEach { file ->
            file.delete()
        }

        removeJob(job)
    } catch (e: Exception) {
        e.printStackTrace()
        logger.error(e.toString())
    }
}

fun compareJobs( leftJob: BlisterJob, rightJob: BlisterJob ) : Int
{
    val comparePatient = leftJob.patient.id.compareTo(rightJob.patient.id)
    if( comparePatient != 0 ) {
        return comparePatient
    }

    val leftFirstDate = leftJob.getEarliestDate()
    val rightFirstDate = rightJob.getEarliestDate()

    val compareFirstDate = leftFirstDate?.compareTo(rightFirstDate) ?: -1
    if( compareFirstDate != 0 ) {
        return compareFirstDate
    }

    val leftEarliestTime = leftJob.getEarliestTime()
    val rightEarliestTime = rightJob.getEarliestTime()
    val compareEarliestTime = leftEarliestTime?.compareTo(rightEarliestTime) ?: -1
    if( compareEarliestTime != 0 ) {
        return compareEarliestTime
    }

    return leftJob.id.compareTo(rightJob.id)
}

fun compareJobs( leftJob: Job, leftPatient: Patient, rightJob: Job, rightPatient: Patient ) : Int
{
    val comparePatient = leftPatient.id.compareTo(rightPatient.id)
    if( comparePatient != 0 ) {
        return comparePatient
    }

    val leftFirstDate = leftJob.getStartDate()
    val rightFirstDate = rightJob.getStartDate()

    val compareFirstDate = leftFirstDate?.compareTo(rightFirstDate) ?: -1
    if( compareFirstDate != 0 ) {
        return compareFirstDate
    }

    val leftEarliestTime = leftJob.getEarliestTime()
    val rightEarliestTime = rightJob.getEarliestTime()
    val compareEarliestTime = leftEarliestTime?.compareTo(rightEarliestTime) ?: -1
    if( compareEarliestTime != 0 ) {
        return compareEarliestTime
    }

    return leftJob.id.compareTo(rightJob.id)
}

data class BlisterPatient(
    @JacksonXmlProperty(localName = "ID")
    @JsonProperty("ID")
    val id: String,

    @JacksonXmlProperty(localName = "Title")
    @JsonProperty("Title")
    val title: String?,

    @JacksonXmlProperty(localName = "Name1")
    @JsonProperty("Name1")
    val name1: String,

    @JacksonXmlProperty(localName = "Name2")
    @JsonProperty("Name2")
    val name2: String,

    @JacksonXmlProperty(localName = "FotoData")
    @JsonProperty("FotoData")
    val fotoData: String?,

    @JacksonXmlProperty(localName = "FotoFile")
    @JsonProperty("FotoFile")
    val fotoFile: String?,

    @JsonSerialize(using = CustomLocalDateSerializer::class)
    @JsonDeserialize(using = CustomLocalDateDeserializer::class)
    @JacksonXmlProperty(localName = "Birthday")
    @JsonProperty("Birthday")
    val birthday: LocalDate,

    @JacksonXmlProperty(localName = "InsuranceID")
    @JsonProperty("InsuranceID")
    val insuranceID: String?,

    @JacksonXmlProperty(localName = "HealthInsuranceID")
    @JsonProperty("HealthInsuranceID")
    val healthInsuranceID: String?,

    @JacksonXmlProperty(localName = "HealthInsuranceName")
    @JsonProperty("HealthInsuranceName")
    val healthInsuranceName: String?,

    @JacksonXmlProperty(localName = "Address")
    @JsonProperty("Address")
    val address: Address?,

    @JacksonXmlProperty(localName = "NursingHome")
    @JsonProperty("NursingHome")
    val nursingHome: NursingHome?
)
data class BlisterJob(
    @JacksonXmlProperty(localName = "ID")
    @JsonProperty("ID")
    val id: String,

    @JacksonXmlProperty(localName = "localID")
    @JsonProperty("localID")
    val localID: String,

    @JacksonXmlProperty(localName = "blisterOrderID")
    @JsonProperty("blisterOrderID")
    val blisterOrderID: String,

    @JsonSerialize(using = CustomLocalDateSerializer::class)
    @JsonDeserialize(using = CustomLocalDateDeserializer::class)
    @JacksonXmlProperty(localName = "CreateDate")
    @JsonProperty("CreateDate")
    val createDate: LocalDate,

    @JsonSerialize(using = CustomLocalTimeSerializer::class)
    @JsonDeserialize(using = CustomLocalTimeDeserializer::class)
    @JacksonXmlProperty(localName = "CreateTime")
    @JsonProperty("CreateTime")
    val createTime: LocalTime,

    @JacksonXmlProperty(localName = "BlisterCardID")
    @JsonProperty("BlisterCardID")
    val blisterCardID: String?,

    @JacksonXmlProperty(localName = "Type")
    @JsonProperty("Type")
    val type: String,

    @JacksonXmlProperty(localName = "Patient")
    @JsonProperty("Patient")
    val patient: BlisterPatient,

    @JacksonXmlProperty(localName = "Medications")
    @JsonProperty("Medications")
    val medications: List<Medication>,

    @JacksonXmlProperty(localName = "Drugs")
    @JsonProperty("Drugs")
    val drugs: List<Drug>,

    @JacksonXmlProperty(localName = "Doctors")
    @JsonProperty("Doctors")
    val doctors: List<Doctor>,

    @JacksonXmlProperty(localName = "Pharmacy")
    @JsonProperty("Pharmacy")
    val pharmacy: Pharmacy,

    @JacksonXmlProperty(localName = "index")
    @JsonProperty("index")
    val index: Int?,

    @JacksonXmlProperty(localName = "indexOf")
    @JsonProperty("indexOf")
    val indexOf: Int?
) {
    @JsonIgnore
    fun fileName() : String {
        return "job_$localID.job.json"
    }

    @JsonIgnore
    fun getIndexLocalized(): String? {
        if(index == null || indexOf == null)
            return null
        return "$index von $indexOf"
    }

    @JsonIgnore
    fun getStartDate(blisterConfig: BlisterConfiguration?): LocalDate? {
        //return lastDate?.with(TemporalAdjusters.next(DayOfWeek.MONDAY))
        return getEarliestDate()
    }

    @JsonIgnore
    fun getEndDate(blisterConfig: BlisterConfiguration): LocalDate? {
        return getLatestDate()
    }

    @JsonIgnore
    fun getEarliestDate(): LocalDate? {
        return medications.minByOrNull { it.takingTime }?.takingDate
    }

    @JsonIgnore
    fun getLatestDate(): LocalDate? {
        return medications.maxByOrNull { it.takingDate }?.takingDate
    }

    @JsonIgnore
    fun getEarliestTime(): LocalTime? {
        return medications.minByOrNull { it.takingTime }?.takingTime
    }

    @JsonIgnore
    fun getLatestTime(): LocalTime? {
        return medications.maxByOrNull { it.takingDate }?.takingTime
    }

    fun getTakingDates(config: BlisterConfiguration): List<LocalDate> {
        return medications.map { it.takingDate }.distinct().sorted()
    }

    companion object {
        fun load(path: String): BlisterJob {
            val objectMapper = ObjectMapper()
            objectMapper.registerModule(JavaTimeModule())
            return objectMapper.readValue(File(path), BlisterJob::class.java)
        }
    }
}