package de.fishbyte.mrblister.model

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.dataformat.xml.XmlMapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement

enum class MedicationStatus {
    Pending,
    InProgress,
    Completed,
    Cancelled
}

@JacksonXmlRootElement(localName = "medicationStatus")
class MedicationStatusModel {
    @JsonProperty("status")
    @JacksonXmlProperty(localName = "status")
    var status: MedicationStatus = MedicationStatus.Pending

    @JsonProperty("drugId")
    @JacksonXmlProperty(localName = "drugId")
    var drugId: String = ""

    @JsonProperty("cancelReason")
    @JacksonXmlProperty(localName = "cancelReason")
    var cancelReason: String = ""
}
