package de.fishbyte.mrblister.model

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.annotation.JsonProperty
import de.fishbyte.mrblister.app.Settings
import java.io.File
import org.mindrot.jbcrypt.BCrypt

enum class UserRole {
    ADMIN,
    USER
}

data class User(
    @JsonProperty("name")
    val name: String,

    @JsonProperty("pinHash")
    val pinHash: String,

    @JsonProperty("role")
    val role: UserRole
) {
    fun matchPassword(password: String?): Boolean {
        if (password == null ) return false
        return UserRepository.verifyPassword(password,pinHash)
    }
}

data class Users(
    @JsonProperty("users")
    val users: List<User>
)

class UserRepository {
    private val users: MutableList<User> = mutableListOf()
    private var settings: Settings? = null

    fun addUser(user: User) {
        users.add(user)
    }

    fun removeUser(user: User) {
        users.remove(user)
    }

    private fun updateUser(oldUser: User, newUser: User) {
        val index = users.indexOf(oldUser)
        if (index >= 0) {
            users[index] = newUser
        }
    }

    fun changePassword(user: User, newPassword: String): User {
        val newUser = user.copy(pinHash = hashPassword(newPassword))
        updateUser(user, newUser)
        return newUser
    }

    fun getUserByName(name: String): User? {
        val locale = java.util.Locale.getDefault()
        val searchName = name.lowercase(locale)
        return users.find { it.name.lowercase(locale) == searchName }
    }

    fun getUsers(): List<User> {
        return users.toList()
    }

    fun saveUsers() {
        if (settings == null) {
            throw IllegalStateException("Settings not initialized. Call loadUsers first.")
        }
        saveUsers(settings!!)
    }

    private fun saveUsers(settings: Settings) {
        val jsonMapper = ObjectMapper()
        val file = File(settings.userFilePath)
        jsonMapper.writeValue(file, Users(users))
    }

    companion object {
        fun loadUsers(repository: UserRepository, settings: Settings) {
            val jsonMapper = ObjectMapper()

            val file = File(settings.userFilePath)
            if (file.exists()) {
                val userList = jsonMapper.readValue(file, Users::class.java)
                userList.users.forEach { user -> repository.addUser(user) }
            }
            repository.settings = settings
        }

        fun hashPassword(password: String): String {
            return BCrypt.hashpw(password, BCrypt.gensalt(12)) // Adjust the log rounds as needed
        }

        fun verifyPassword(password: String, hashedPassword: String): Boolean {
            try {
                return BCrypt.checkpw(password, hashedPassword)
            } catch (e: Exception) {
                return false
            }
        }
    }
}