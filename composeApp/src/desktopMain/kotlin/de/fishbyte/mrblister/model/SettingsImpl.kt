package de.fishbyte.mrblister.model

import com.fasterxml.jackson.dataformat.xml.XmlMapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import de.fishbyte.mrblister.app.PicturesSetting
import de.fishbyte.mrblister.app.Settings
import java.io.File

class SettingsImpl : Settings {

    private var _settings: SettingsModelImpl? = null

    override val jobsPath: String
        get() = _settings?.jobsPath ?: ""
    override val userFilePath: String
        get() = _settings?.userFilePath ?: ""
    override val storagePath: String
        get() = _settings?.storagePath ?: ""
    override val picture: PicturesSetting
        get() = _settings?.picture ?: PicturesSetting.NONE
    override val blisterWidth: Int
        get() = _settings?.blisterWidth ?: 0
    override val multiMonitor: Boolean
        get() = _settings?.multiMonitor ?: false
    override val cameraCountDown: Int
        get() = _settings?.cameraCountDown ?: 0
    override val patientScanNag: Boolean
        get() = _settings?.patientScanNag ?: false
    override val medicationScanNag: Boolean
        get() = _settings?.medicationScanNag ?: false
    override val primaryMonitor: String
        get() = _settings?.primaryMonitor ?: ""
    override val secondaryMonitor: String
        get() = _settings?.secondaryMonitor ?: ""
    override val scannerPort: String?
        get() = _settings?.scannerPort
    override val scannerBaudRate: Int?
    get() = _settings?.scannerBaudRate
    override val overscan: Int?
    get() = _settings?.overscan
    override val moveImports: Boolean?
    get() = _settings?.moveImports

    fun setSettings(settings: SettingsModelImpl) {
        _settings = settings
    }
}

data class SettingsModelImpl(
    @JacksonXmlProperty(localName = "JobsPath") override val jobsPath: String,
    @JacksonXmlProperty(localName = "UserFilePath") override val userFilePath: String,
    @JacksonXmlProperty(localName = "StoragePath") override val storagePath: String,
    @JacksonXmlProperty(localName = "Picture") override val picture: PicturesSetting = PicturesSetting.NONE,
    @JacksonXmlProperty(localName = "BlisterWidth") override val blisterWidth: Int = 0,
    @JacksonXmlProperty(localName = "MultiMonitor") override val multiMonitor: Boolean = false,
    @JacksonXmlProperty(localName = "CameraCountDown") override val cameraCountDown: Int = 0,
    @JacksonXmlProperty(localName = "PatientScanNag") override val patientScanNag: Boolean = false,
    @JacksonXmlProperty(localName = "MedicationScanNag") override val medicationScanNag: Boolean = false,
    @JacksonXmlProperty(localName = "PrimaryMonitor") override val primaryMonitor: String = "",
    @JacksonXmlProperty(localName = "SecondaryMonitor") override val secondaryMonitor: String = "",
    @JacksonXmlProperty(localName = "ScannerPort") override val scannerPort: String?,
    @JacksonXmlProperty(localName = "ScannerBaud") override val scannerBaudRate: Int?,
    @JacksonXmlProperty(localName = "Overscan") override val overscan: Int?,
    @JacksonXmlProperty(localName = "MoveImports") override val moveImports: Boolean? = true
) : Settings
fun loadSettings(path: String, settings: Settings) {
    val xmlMapper = XmlMapper().apply {
        registerModule(JavaTimeModule())
        configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
    }
    val configFile = File(path)
    val baseDir = configFile.parentFile

    val loadedSettings = xmlMapper.readValue(configFile, SettingsModelImpl::class.java)
    val resolvedSettings = loadedSettings.copy(
        jobsPath = resolvePathIfRelative(baseDir, loadedSettings.jobsPath),
        userFilePath = resolvePathIfRelative(baseDir, loadedSettings.userFilePath.let {
            if( it.endsWith(".xml") ) it.replace(".xml", ".json") else it
        }, default = "users.xml"),
        storagePath = resolvePathIfRelative(baseDir, loadedSettings.storagePath, default = "storage", createIfNotExists = true),
        moveImports = loadedSettings.moveImports ?: true
    )
    (settings as SettingsImpl).setSettings(resolvedSettings)
}

private fun resolvePathIfRelative(baseDir: File, path: String, default: String? = null, createIfNotExists: Boolean = false): String {
    val file = File( if( path.isNullOrBlank() ) default else path)
    val target = if (file.isAbsolute) file else File(baseDir, path)

    if( createIfNotExists && !target.exists() ) {
        target.mkdirs()
    }

    return target.absolutePath
}

/*
fun saveSettings(settings: Settings, path: String) {
    val xmlMapper = XmlMapper().apply {
        registerModule(JavaTimeModule())
    }
    xmlMapper.writeValue(File(path), settings)
}
 */
