package de.fishbyte.mrblister.services

import de.fishbyte.mrblister.app.Settings
import de.fishbyte.mrblister.app.views.tools.formatShortDate
import de.fishbyte.mrblister.app.views.tools.name
import de.fishbyte.mrblister.model.*
import org.apache.lucene.analysis.standard.StandardAnalyzer
import org.apache.lucene.document.*
import org.apache.lucene.index.DirectoryReader
import org.apache.lucene.index.IndexWriter
import org.apache.lucene.index.IndexWriterConfig
import org.apache.lucene.queryparser.classic.MultiFieldQueryParser
import org.apache.lucene.queryparser.classic.QueryParser
import org.apache.lucene.search.IndexSearcher
import org.apache.lucene.search.MatchAllDocsQuery
import org.apache.lucene.search.Sort
import org.apache.lucene.search.SortField
import org.apache.lucene.store.FSDirectory
import org.apache.lucene.util.BytesRef
import java.io.File
import java.time.LocalDate
import java.time.LocalDateTime


data class SearchResult(val id: String, val patient: String, val job: String, val order: String, val name: String, val birthday: String?, val startDate: LocalDate, val completed: LocalDateTime, val index: String?, val nursingHome: String?, val directory: String )

class SearchService(private val settings: Settings) {

    private val directory by lazy {
        FSDirectory.open(File(settings.storagePath, "index").toPath())
    }

    private val analyzer = StandardAnalyzer()

    // Function to add a document to the index
    fun addDocument(job: BlisterJob, completed: LocalDateTime, targetDirectory: String) {

        val config = IndexWriterConfig(analyzer)
        val writer = IndexWriter(directory, config)

        val doc = Document()
        doc.add(StringField("id", job.id, Field.Store.YES))
        doc.add(StringField("localID", job.localID, Field.Store.YES))
        doc.add(StringField("order", job.blisterOrderID, Field.Store.YES))
        doc.add(StringField("patient", job.patient.id, Field.Store.YES))
        doc.add(TextField("name", name(job.patient), Field.Store.YES))
        doc.add(TextField("drugs", drugs(job), Field.Store.YES))
        doc.add(StringField("startDate", job.getEarliestDate().toString(), Field.Store.YES))
        doc.add(StringField("completed", completed.toString(), Field.Store.YES))
        doc.add(SortedDocValuesField("completed", BytesRef(completed.toString())))
        doc.add(StringField("directory", targetDirectory, Field.Store.YES))
        if( job.patient.birthday != null ) {
            doc.add(StringField("birthday", formatShortDate(job.patient.birthday), Field.Store.YES))
        }

        val index = job.getIndexLocalized()
        if( index?.isNotEmpty() == true ) {
            doc.add(StringField("index", index, Field.Store.YES))
        }
        if( job.patient.nursingHome?.name?.isNotEmpty() == true ) {
            doc.add(TextField("nursingHome", job.patient.nursingHome!!.name, Field.Store.YES))
        }
        writer.addDocument(doc)
        writer.close()
    }

    // Function to search the index
    fun searchIndex(queryStr: String) : List<SearchResult> {

        if( queryStr?.isNotEmpty() != true ) {
            return emptyList()
        }

        val reader = DirectoryReader.open(directory)
        val searcher = IndexSearcher(reader)
        // Parse query
        val fields = arrayOf("name", "drugs", "nursingHome")
        val parser = MultiFieldQueryParser(fields, analyzer)
//        val parser = QueryParser("name", analyzer)
        val query = parser.parse(queryStr + "*")

        // Perform search
        val hits = searcher.search(query, 10)

        val results = mutableListOf<SearchResult>()

        for (hit in hits.scoreDocs) {
            val doc = searcher.doc(hit.doc)

            results.add(SearchResult(
                id = doc.get("localID"),
                patient = doc.get("patient"),
                job = doc.get("id"),
                order = doc.get("order"),
                name = doc.get("name"),
                birthday = doc.get("birthday"),
                startDate = LocalDate.parse(doc.get("startDate")),
                completed = LocalDateTime.parse(doc.get("completed")),
                index = doc.get("index"),
                nursingHome = doc.get("nursingHome"),
                directory = doc.get("directory")
            ))
        }

        reader.close()

        return results
    }

    fun searchLast() : List<SearchResult> {

        val reader = DirectoryReader.open(directory)
        val searcher = IndexSearcher(reader)
        // Parse query
        val fields = arrayOf("name", "drugs", "nursingHome")
        val parser = MultiFieldQueryParser(fields, analyzer)
//        val parser = QueryParser("name", analyzer)
        val query = MatchAllDocsQuery()
        val sort = Sort(SortField("completed", SortField.Type.STRING, true)) // true = descending

        // Perform search
        val hits = searcher.search(query, 10, sort)

        val results = mutableListOf<SearchResult>()

        for (hit in hits.scoreDocs) {
            val doc = searcher.doc(hit.doc)

            results.add(SearchResult(
                id = doc.get("localID"),
                patient = doc.get("patient"),
                job = doc.get("id"),
                order = doc.get("order"),
                name = doc.get("name"),
                birthday = doc.get("birthday"),
                startDate = LocalDate.parse(doc.get("startDate")),
                completed = LocalDateTime.parse(doc.get("completed")),
                index = doc.get("index"),
                nursingHome = doc.get("nursingHome"),
                directory = doc.get("directory")
            ))
        }

        reader.close()

        return results
    }

    private fun drugs(job: BlisterJob): String {

        val builder = StringBuilder()
        for (medication in job.medications) {
            val drug = job.drugs.filter { it.id == medication.drugID }.firstOrNull()
            if (drug != null) {
                if (builder.isNotEmpty()) {
                    builder.append(" ")
                }
                builder.append(drug.name)
                for( packingType in drug.packingTypes?.packingTypeList ?: emptyList() ) {
                    builder.append(" ")
                    builder.append(packingType.id)
                }
            }
        }

        return builder.toString()
    }
}

