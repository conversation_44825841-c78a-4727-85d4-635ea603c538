package de.fishbyte.mrblister.services

import javax.sound.sampled.AudioSystem
import javax.sound.sampled.Clip
import java.io.File

class AudioPlayer {
    fun playSound(filePath: String) {
        try {
            val audioFile = File(filePath)
            val audioStream = AudioSystem.getAudioInputStream(audioFile)
            val clip = AudioSystem.getClip()
            clip.open(audioStream)
            clip.start()
        } catch (e: Exception) {
            //LoggerService.logger.error("Failed to play audio file: $filePath", e)
        }
    }

    fun playSoundWithCallback(filePath: String, onComplete: () -> Unit) {
        try {
            val audioFile = File(filePath)
            val audioStream = AudioSystem.getAudioInputStream(audioFile)
            val clip = AudioSystem.getClip()
            clip.open(audioStream)

            clip.addLineListener { event ->
                if (event.type == javax.sound.sampled.LineEvent.Type.STOP) {
                    onComplete()
                    clip.close()
                }
            }

            clip.start()
        } catch (e: Exception) {
           //LoggerService.logger.error("Failed to play audio file: $filePath", e)
            onComplete()
        }
    }
}
