package de.fishbyte.mrblister.services

import de.fishbyte.mrblister.app.AppViewModel
import de.fishbyte.mrblister.model.UserRepository
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.apache.logging.log4j.core.config.Configurator
import org.apache.logging.log4j.core.config.builder.api.ConfigurationBuilderFactory
import java.io.File
import java.nio.file.Path

class LoggerService(configPath: String, private val appViewModel: AppViewModel) {
    private val logger: Logger

    init {

        System.setProperty("log4j2.disable.jmx", "true")

        val builder = ConfigurationBuilderFactory.newConfigurationBuilder()

        builder.setStatusLevel(org.apache.logging.log4j.Level.INFO)
        builder.setConfigurationName("MrBlisterConfig")

        val logPath = File( configPath, "logs")

        val fileName = File( logPath, "mrblister.log").toPath().toString()
        val filePattern = File( logPath, "mrblister-%d{yyyy-MM-dd}-%i.log.gz").toPath().toString()
        // Create appender
        val appender = builder.newAppender("RollingFile", "RollingFile")
        appender.addAttribute("fileName", fileName)
        appender.addAttribute("filePattern", filePattern)

        // Create layout
        val layout = builder.newLayout("PatternLayout")
        layout.addAttribute("pattern", "%d{HH:mm:ss.SSS} - [%X{username}] - %msg%n")
        appender.add(layout)

        // Add policies
        val policies = builder.newComponent("Policies")
        policies.addComponent(builder.newComponent("SizeBasedTriggeringPolicy").addAttribute("size", "25MB"))
        policies.addComponent(builder.newComponent("TimeBasedTriggeringPolicy").addAttribute("interval", "1"))
        appender.addComponent(policies)

        // Add rollover strategy
        appender.addComponent(builder.newComponent("DefaultRolloverStrategy").addAttribute("max", "30"))

        builder.add(appender)

        // Create root logger
        val rootLogger = builder.newRootLogger(org.apache.logging.log4j.Level.INFO)
        rootLogger.add(builder.newAppenderRef("RollingFile"))
        builder.add(rootLogger)

        // Initialize configuration
        Configurator.initialize(builder.build())
        logger = LogManager.getLogger(LoggerService::class.java)
    }

    fun info(message: String) {
        org.apache.logging.log4j.ThreadContext.put("username", appViewModel.user.value?.name ?: "no-user")
        logger.info(message)
        org.apache.logging.log4j.ThreadContext.remove("username")
    }

    fun error(message: String) {
        org.apache.logging.log4j.ThreadContext.put("username", appViewModel.user.value?.name ?: "no-user")
        logger.error(message)
        org.apache.logging.log4j.ThreadContext.remove("username")
    }
}
