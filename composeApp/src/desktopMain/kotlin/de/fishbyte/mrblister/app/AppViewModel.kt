package de.fishbyte.mrblister.app

import androidx.compose.runtime.Composable
import com.fazecast.jSerialComm.SerialPort
import com.fazecast.jSerialComm.SerialPortDataListener
import com.fazecast.jSerialComm.SerialPortEvent
import de.fishbyte.mrblister.app.views.AppNavigation
import de.fishbyte.mrblister.app.views.KeyboardHandler
import de.fishbyte.mrblister.app.views.job.BlisterJobViewModel
import de.fishbyte.mrblister.app.views.joblist.JobListViewModel
import de.fishbyte.mrblister.app.views.login.LoginViewModel
import de.fishbyte.mrblister.app.views.tools.Barcode
import de.fishbyte.mrblister.app.views.tools.BarcodeType
import de.fishbyte.mrblister.model.*
import de.fishbyte.mrblister.services.LoggerService
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.awt.GraphicsDevice
import java.io.File

class AppViewModel( val settings: Settings, val navigation: AppNavigation, val userRepository: UserRepository, val configPath: String) {

    private val _user = MutableStateFlow<User?>(null)
    val user: StateFlow<User?> get() = _user.asStateFlow()

    private val _jobs = MutableStateFlow<List<BlisterJobViewModel>>(emptyList())
    val jobs: StateFlow<List<BlisterJobViewModel>> get() = _jobs.asStateFlow()

    private val _blisterJobs = MutableStateFlow<Map<String,BlisterOrder>>(emptyMap())
    val blisterJobs: StateFlow<Map<String,BlisterOrder>> get() = _blisterJobs.asStateFlow()

    private val _alertMessage = MutableStateFlow<String?>(null)
    val alertMessage: StateFlow<String?> get() = _alertMessage.asStateFlow()

    private val _jobStatus = MutableStateFlow<List<JobStatusModel>>(emptyList())
    val jobStatus: StateFlow<List<JobStatusModel>> get() = _jobStatus.asStateFlow()

    // Add near the other state declarations at the top
    private val _dialog = MutableStateFlow<(@Composable () -> Unit)?>(null)
    val dialog: StateFlow<(@Composable () -> Unit)?> get() = _dialog.asStateFlow()

    val logger: LoggerService by lazy { LoggerService(configPath, this) }

    var primaryScreen: GraphicsDevice? = null
    var secondaryScreen: GraphicsDevice? = null

    var blisterConfiguration: List<BlisterConfiguration> = emptyList()

    // Add helper methods to show/hide dialog
    fun showDialog(content: @Composable () -> Unit) {
        _dialog.value = content
    }

    fun dismissDialog() {
        _dialog.value = null
    }

    fun updateBlisterJobs(jobs: List<BlisterJobViewModel>, blister: Map<String,BlisterOrder>) {

        _jobs.value = jobs.sortedWith {
            job1, job2 -> compareJobs(job1.job, job2.job)
        }
        _blisterJobs.value = blister
    }

    fun updateJobStatus(status: List<JobStatusModel>) {
        _jobStatus.value = status
    }

    fun findJobStatus(localID: String): JobStatusModel? {
        return _jobStatus.value.find { it.localID == localID }
    }

    fun getOrCreateJobStatus(job: BlisterJob): JobStatusModel {
        var jobStatus = findJobStatus(job.localID)
        if( jobStatus == null ) {
            jobStatus = JobStatusModel(localID = job.localID, status = JobStatus.Pending)
            _jobStatus.value += jobStatus
        }
        return jobStatus
    }

    fun showAlert(message: String) : Unit {
        _alertMessage.value = message
    }

    fun dismissAlert() {
        _alertMessage.value = null
    }

    fun importsStoragePath() = File( settings.storagePath, "imports")
    fun blisterStoragePath() = File( settings.storagePath, "blister" )
    fun jobStatusStoragePath() = File( settings.storagePath, "jobstatus" )
    fun imageStoragePath() =  File( settings.storagePath, "images" )
    fun archivePath() = File( settings.storagePath, "archive" )

    fun handleBarcode(barcode: String) {

        val top = navigation.top()
        if (top is KeyboardHandler) {
            top.handleBarcode(barcode);
        }
    }

    fun handleActionKey(key: Long) {
        val top = navigation.top()
        if (top is KeyboardHandler ) {
            top.handleActionKey(key)
        }
    }

    fun loginUser(user: User) {
        _user.value = user
        JobListViewModel.present(this)
    }

    fun logout() {
        _user.value = null
        LoginViewModel.present(this)
    }

    fun cleanup() {
    }

    fun removeJob( job: BlisterJob ) {
        _jobs.value = _jobs.value.filterNot { it.job.localID == job.localID }
    }

    fun loadBlisterConfig() {
        blisterConfiguration = loadBlisterConfig(configPath)
    }

    fun saveBlisterConfigurations(configurations: List<BlisterConfiguration>) {
        blisterConfiguration = configurations
        saveBlisterConfig(configPath, configurations)
    }

    fun connectScanner() {

        if( settings.scannerPort.isNullOrEmpty() ) {
            return
        }

        val serialPort = openSerialPort(settings.scannerPort ?: "", settings.scannerBaudRate ?: 9600 )
        if( serialPort != null ) {
            println("Serial port opened")
            serialPort.addDataListener(object : SerialPortDataListener {
                override fun getListeningEvents(): Int {
                    return SerialPort. LISTENING_EVENT_DATA_AVAILABLE
                }

                override fun serialEvent(event: SerialPortEvent) {
                    if (event.eventType == SerialPort.LISTENING_EVENT_DATA_AVAILABLE) {
                        val data = ByteArray(serialPort.bytesAvailable())
                        val numRead = serialPort.readBytes(data, data.size.toInt())
                        val dataAsString = String(data, 0, numRead)

                        // Convert to escaped string for Kotlin source code
                        val escapedString = dataAsString
                            .replace("\\", "\\\\")  // Escape backslashes
                            .replace("\t", "\\t")   // Escape tabs
                            .replace("\r", "\\r")   // Escape carriage returns
                            .replace("\n", "\\n")   // Escape newlines
                            .replace("\"", "\\\"")  // Escape double quotes
                            .let { str ->
                                val sb = StringBuilder()
                                for (c in str) {
                                    if (c.code > 127 || c.code < 32) {
                                        // Convert non-ASCII and control characters to Unicode escape sequences
                                        sb.append(String.format("\\u%04X", c.code))
                                    } else {
                                        sb.append(c)
                                    }
                                }
                                sb.toString()
                            }

                        println("Data received: \"$escapedString\"")

                        val decoded = Barcode.parse(dataAsString)
                        if( decoded.type == BarcodeType.GS1_DATAMATRIX ) {
                            println("GS1: pzn=${decoded.pzn}, exp=${decoded.expiryDate}, batch=${decoded.batchNumber}")
                        }
                        handleBarcode(dataAsString)
                    }
                }
            })
        } else {
            throw RuntimeException("Die Verbindung zum konfigurierten Scanner konnte nicht hergestellt werden.")
        }
    }
}
