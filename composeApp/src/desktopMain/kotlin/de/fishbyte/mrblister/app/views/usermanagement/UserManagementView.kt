package de.fishbyte.mrblister.app.views.usermanagement

import Dialog
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.AppViewModel
import de.fishbyte.mrblister.app.views.joblist.JobListViewModel
import de.fishbyte.mrblister.components.Components
import de.fishbyte.mrblister.components.RootViewComponent
import de.fishbyte.mrblister.model.User
import de.fishbyte.mrblister.model.UserRole
import kotlinx.coroutines.flow.StateFlow
import org.koin.compose.getKoin

@Composable
fun UserManagementView() {
    val appViewModel = getKoin().get<AppViewModel>()
    val viewModel = remember { UserManagementViewModel(appViewModel) }
    
    val users by viewModel.users.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    val successMessage by viewModel.successMessage.collectAsState()
    val selectedUser by viewModel.selectedUser.collectAsState()
    
    // State for dialogs
    var showPasswordDialog by remember { mutableStateOf(false) }
    var showDeleteConfirmDialog by remember { mutableStateOf(false) }
    var userToDelete by remember { mutableStateOf<User?>(null) }
    
    RootViewComponent(
        footer = {
                Components.secondaryButton("Zurück", {
                    JobListViewModel.present(viewModel.appViewModel)
                })
            Spacer(modifier = Modifier.weight(1f))
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = { JobListViewModel.present(viewModel.appViewModel) }) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "Zurück")
                }
                Text(
                    "Benutzerverwaltung",
                    style = MaterialTheme.typography.headlineMedium,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Error message
            errorMessage?.let { message ->
                ErrorMessage(message) {
                    viewModel.dismissErrorMessage()
                }
            }
            
            // Success message
            successMessage?.let { message ->
                SuccessMessage(message) {
                    viewModel.dismissSuccessMessage()
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Main content
            Row(
                modifier = Modifier.fillMaxSize(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // User list
                UserList(
                    users = users,
                    onEditPassword = { user ->
                        viewModel.selectUser(user)
                        showPasswordDialog = true
                    },
                    onDeleteUser = { user ->
                        userToDelete = user
                        showDeleteConfirmDialog = true
                    },
                    modifier = Modifier.weight(1f)
                )
                
                // Add user form
                AddUserForm(
                    viewModel = viewModel,
                    modifier = Modifier.weight(1f)
                )
            }
        }
        
        // Password change dialog
        if (showPasswordDialog && selectedUser != null) {
            PasswordChangeDialog(
                user = selectedUser!!,
                newPassword = viewModel.newPassword,
                confirmPassword = viewModel.confirmPassword,
                onUpdateNewPassword = viewModel::updateNewPassword,
                onUpdateConfirmPassword = viewModel::updateConfirmPassword,
                onChangePassword = {
                },
                onDismiss = { confirm ->
                    if( confirm ) {
                        viewModel.changePassword()
                    }
                    showPasswordDialog = false
                    viewModel.clearPasswordForm()
                }
            )
        }
        
        // Delete confirmation dialog
        if (showDeleteConfirmDialog && userToDelete != null) {
            DeleteUserConfirmDialog(
                user = userToDelete!!,
                onConfirm = {},
                onDismiss = { confirmed ->
                    if(confirmed) {
                        viewModel.removeUser(userToDelete!!)
                    }
                    showDeleteConfirmDialog = false
                    userToDelete = null
                }
            )
        }
    }
}

@Composable
fun ErrorMessage(message: String, onDismiss: () -> Unit) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFFDEDED)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = message,
                color = Color(0xFFB71C1C)
            )
            IconButton(onClick = onDismiss) {
                Text("×", fontSize = MaterialTheme.typography.titleLarge.fontSize)
            }
        }
    }
}

@Composable
fun SuccessMessage(message: String, onDismiss: () -> Unit) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFE8F5E9)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = message,
                color = Color(0xFF1B5E20)
            )
            IconButton(onClick = onDismiss) {
                Text("×", fontSize = MaterialTheme.typography.titleLarge.fontSize)
            }
        }
    }
}

@Composable
fun UserList(
    users: List<User>,
    onEditPassword: (User) -> Unit,
    onDeleteUser: (User) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Text(
                "Benutzer",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Table header
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(MaterialTheme.colorScheme.surfaceVariant)
                    .padding(8.dp)
            ) {
                Text(
                    "Name",
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.weight(1f)
                )
                Text(
                    "Barcode",
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.weight(1f)
                )
                Text(
                    "Rolle",
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.weight(1f)
                )
                Text(
                    "Aktionen",
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.width(120.dp)
                )
            }
            
            // User list
            LazyColumn(
                modifier = Modifier.fillMaxWidth()
            ) {
                items(users) { user ->
                    UserRow(
                        user = user,
                        onEditPassword = { onEditPassword(user) },
                        onDeleteUser = { onDeleteUser(user) }
                    )
                }
            }
        }
    }
}

@Composable
fun UserRow(
    user: User,
    onEditPassword: () -> Unit,
    onDeleteUser: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp, horizontal = 8.dp)
            .border(1.dp, MaterialTheme.colorScheme.outlineVariant, RoundedCornerShape(4.dp))
            .padding(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            user.name,
            modifier = Modifier.weight(1f)
        )
        Text(
            when (user.role) {
                UserRole.ADMIN -> "Administrator"
                UserRole.USER -> "Benutzer"
            },
            modifier = Modifier.weight(1f)
        )
        Row(
            modifier = Modifier.width(120.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            IconButton(
                onClick = onEditPassword,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    Icons.Default.Edit,
                    contentDescription = "Passwort ändern",
                    tint = MaterialTheme.colorScheme.primary
                )
            }
            IconButton(
                onClick = onDeleteUser,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    Icons.Default.Delete,
                    contentDescription = "Benutzer löschen",
                    tint = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}

@Composable
fun AddUserForm(
    viewModel: UserManagementViewModel,
    modifier: Modifier = Modifier
) {
    val newUserName by viewModel.newUserName.collectAsState()
    val newUserPassword by viewModel.newUserPassword.collectAsState()
    val newUserRole by viewModel.newUserRole.collectAsState()
    
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Text(
                "Neuen Benutzer hinzufügen",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Form fields
            OutlinedTextField(
                value = newUserName,
                onValueChange = viewModel::updateNewUserName,
                label = { Text("Benutzername") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
            
            Spacer(modifier = Modifier.height(8.dp))

            OutlinedTextField(
                value = newUserPassword,
                onValueChange = viewModel::updateNewUserPassword,
                label = { Text("Passwort") },
                visualTransformation = PasswordVisualTransformation(),
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Role selection
            Text(
                "Rolle",
                style = MaterialTheme.typography.bodyMedium
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = newUserRole == UserRole.USER,
                    onClick = { viewModel.updateNewUserRole(UserRole.USER) }
                )
                Text(
                    "Benutzer",
                    modifier = Modifier.clickable { viewModel.updateNewUserRole(UserRole.USER) }
                )
                
                Spacer(modifier = Modifier.width(16.dp))
                
                RadioButton(
                    selected = newUserRole == UserRole.ADMIN,
                    onClick = { viewModel.updateNewUserRole(UserRole.ADMIN) }
                )
                Text(
                    "Administrator",
                    modifier = Modifier.clickable { viewModel.updateNewUserRole(UserRole.ADMIN) }
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Submit button
            Button(
                onClick = viewModel::addUser,
                modifier = Modifier.align(Alignment.End)
            ) {
                Text("Benutzer hinzufügen")
            }
        }
    }
}

@Composable
fun PasswordChangeDialog(
    user: User,
    newPassword: StateFlow<String>,
    confirmPassword: StateFlow<String>,
    onUpdateNewPassword: (String) -> Unit,
    onUpdateConfirmPassword: (String) -> Unit,
    onChangePassword: () -> Unit,
    onDismiss: (confirmed: Boolean) -> Unit
) {
    val newPasswordValue by newPassword.collectAsState()
    val confirmPasswordValue by confirmPassword.collectAsState()
    
    Dialog(
        title = "Passwort ändern für ${user.name}",
        content = {
            Column {
                OutlinedTextField(
                    value = newPasswordValue,
                    onValueChange = onUpdateNewPassword,
                    label = { Text("Neues Passwort") },
                    visualTransformation = PasswordVisualTransformation(),
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                OutlinedTextField(
                    value = confirmPasswordValue,
                    onValueChange = onUpdateConfirmPassword,
                    label = { Text("Passwort bestätigen") },
                    visualTransformation = PasswordVisualTransformation(),
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
            }
        },
        onCloseMessage = "Abbrechen",
        onClose = onDismiss,
        onConfirmMessage = "Passwort ändern",
        onConfirm = onChangePassword
    )
}

@Composable
fun DeleteUserConfirmDialog(
    user: User,
    onConfirm: () -> Unit,
    onDismiss: (confirmed: Boolean) -> Unit
) {
    Dialog(
        title = "Benutzer löschen",
        message = "Möchten Sie den Benutzer ${user.name} wirklich löschen?",
        onCloseMessage = "Abbrechen",
        onClose = onDismiss,
        onConfirmMessage = "Löschen",
        onConfirm = onConfirm
    )
}
