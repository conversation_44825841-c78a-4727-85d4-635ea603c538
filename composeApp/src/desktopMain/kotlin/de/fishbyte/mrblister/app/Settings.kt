package de.fishbyte.mrblister.app

enum class PicturesSetting {
    NONE,
    MEDICATION,
    JOB,
    ALL
}

interface Settings {
    val jobsPath: String
    val userFilePath: String
    val storagePath: String
    val picture: PicturesSetting
    val blisterWidth: Int
    val multiMonitor: Boolean
    val cameraCountDown: Int
    val patientScanNag: Boolean
    val medicationScanNag: Boolean
    val primaryMonitor: String?
    val secondaryMonitor: String?
    val scannerPort: String?
    val scannerBaudRate: Int?
    val overscan: Int?
    val moveImports: Boolean?
}
