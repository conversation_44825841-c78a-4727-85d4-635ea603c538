package de.fishbyte.mrblister.app.views.tools

import androidx.compose.runtime.Composable
import de.fishbyte.mrblister.app.AppViewModel
import de.fishbyte.mrblister.model.BlisterOrder
import de.fishbyte.mrblister.services.LoggerService
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.koin.compose.getKoin
import java.io.File

private fun padBarcode(barcode: String): String {
    return padBarcode(barcode,12)
}

private fun padBarcode(barcode: String, length: Int): String {
    return barcode.padStart(length, '0')
}

fun generateBarcodeHtml(orders: List<BlisterOrder>, outputPath: String, logger: LoggerService) {

    logger.info("Starting barcode HTML generation for ${orders.size} orders")

    val header = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>MrBlister Barcodes</title>
            <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.6/dist/JsBarcode.all.min.js"></script>
            <style>
                .grid-container {
                    display: grid;
                    grid-template-columns: repeat(3, 1fr);
                    gap: 20px;
                    padding: 20px;
                }
                .grid-item {
                    text-align: center;
                    padding: 10px;
                }
                .item-title {
                    font-weight: bold;
                    margin-bottom: 10px;
                    min-height: 40px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                @media print {
                    /* Force a page break before this element */
                    .page-break-before {
                        page-break-before: always;
                    }

                    /* Force a page break after this element */
                    .page-break-after {
                        page-break-after: always;
                    }

                    /* Avoid page breaks inside this element */
                    .no-page-break-inside {
                        page-break-inside: avoid;
                    }
                }
            </style>
        </head>
        <body>
            <h1>Patienten</h1>
    """

    val html = StringBuilder()
    html.append(header)

    val patients = orders.flatMap { it.patients.patientList }.toList()
    val drugs = orders.flatMap { it.drugs.drugList }.toList()

    for (patient in patients) {
        html.append("<div class=\"grid-item no-page-break-inside\">\r\n")
        html.append("<div class=\"item-title\">${patient.jobs.jobList.get(0).id} - ${patient.name1} ${patient.name2}</div>\r\n")
        html.append("<svg class=\"barcode\" jsbarcode-format=\"code39\" jsbarcode-value=\"${patient.id}\" jsbarcode-textmargin=\"0\" jsbarcode-fontoptions=\"bold\"></svg>\r\n")
        html.append("</div>\r\n</div>\r\n")

        html.append("<h1>Medikamente</h1>\r\n<div class=\"grid-container page-break-after\">\r\n")

        for (drug in drugs) {

            var barcode = drug.packingTypes?.packingTypeList?.get(0)?.id ?: null

            if( barcode == null ) {
                continue
            }
            barcode = padBarcode(barcode,7)

            for( job in patient.jobs.jobList ) {
                for( medication in job.medications.medicationList ) {
                    if( medication.drugID == drug.id ) {
                        html.append("<div class=\"grid-item no-page-break-inside\">\r\n")
                        html.append("<div class=\"item-title\">${drug.name}</div>\r\n")
                        html.append("<img alt='Barcode Generator TEC-IT' src='https://barcode.tec-it.com/barcode.ashx?data=${barcode}&amp;code=Health_PZN${barcode.length}'/>")
                        html.append("</div>\r\n")
                        break
                    }
                }
            }
        }

        html.append("</div>\r\n")
    }

    html.append("<script>JsBarcode(\".barcode\").init(); </script>")
    html.append("</body>\r\n</html>")

    logger.info("Generated barcodes for ${patients.size} patients and ${drugs.size} drugs")
    logger.info("Saving barcode HTML to: $outputPath")

    File(outputPath).writeText(html.toString())
    logger.info("Barcode HTML generation completed successfully")
}