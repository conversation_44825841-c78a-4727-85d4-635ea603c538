package de.fishbyte.mrblister.app.views.calibration

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.*
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.AppViewModel
import de.fishbyte.mrblister.app.Settings
import de.fishbyte.mrblister.app.views.blisterconfig.*
import de.fishbyte.mrblister.app.views.job.MedicationGridView
import de.fishbyte.mrblister.app.views.joblist.JobListViewModel
import de.fishbyte.mrblister.components.Components
import de.fishbyte.mrblister.components.Components.Companion.primaryButton
import de.fishbyte.mrblister.components.Components.Companion.secondaryButton
import de.fishbyte.mrblister.components.RootSplitViewComponent
import de.fishbyte.mrblister.components.RootViewComponent
import org.koin.mp.KoinPlatform.getKoin

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CalibrationView(viewModel: CalibrationViewModel) {
    val blisterWidth = getKoin().get<Settings>().blisterWidth.dp
    val appViewModel = getKoin().get<AppViewModel>()

    val marginX by viewModel.marginX.collectAsState()
    val marginY by viewModel.marginY.collectAsState()

    val cursor = mutableStateOf<Offset?>( null )
    val areaSize = mutableStateOf<IntSize?>( null)

    val config = viewModel.selectedConfig.collectAsState()


    RootSplitViewComponent(footer = {
        Components.secondaryButton(
            text = "Zurück",
            onClick = {
                cancel(viewModel,appViewModel)
            }
        )
        Spacer(modifier = Modifier.weight(1f))
        primaryButton(
            text = "Speichern",
            onClick = { save(viewModel,appViewModel) })
    }, content = {

        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = {
                cancel(viewModel, appViewModel)
            }) {
                Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Zurück")
            }
            Text(
                "Blister-Konfigurationsverwaltung",
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.padding(start = 8.dp)
            )
        }

        Spacer(modifier = Modifier.height(16.dp))
        Text(
            "Blister ${viewModel.selectedConfig.value?.name ?: ""} Kalibrieren",
            style = MaterialTheme.typography.headlineMedium
        )

        Spacer(modifier = Modifier.height(16.dp))

        ClickIndicatorView(viewModel)
    }, blisterContent = {
                val density = LocalDensity.current

                Box( modifier = Modifier.fillMaxSize().pointerInput(Unit) {
                    awaitPointerEventScope {
                        while(true) {
                            val event = awaitPointerEvent(PointerEventPass.Main)

                            val position = event.changes.first().position
                            val xDp = position.x.toDp()
                            val yDp = position.y.toDp()
                            cursor.value = position

                            if (event.type == PointerEventType.Press) {
                                if (event.buttons.isBackPressed) {
                                    viewModel.resetSelection()
                                } else if (event.buttons.isPrimaryPressed) {
                                    viewModel.nextSelection(xDp, yDp)
                                }
                            }
                        }
                    }
                }.onSizeChanged { size ->
                    areaSize.value = size
                    with(density) {
                        viewModel.updateTotalWidth(size.width.toFloat().toDp())
                        viewModel.updateTotalHeight(size.height.toFloat().toDp())
                    }
                }) {
                    androidx.compose.foundation.Canvas(modifier = Modifier.fillMaxSize()) {

                        val position = cursor.value ?: Offset(0f, 0f)
                        val size = areaSize.value ?: IntSize(0, 0)

                        if( position != null && size != null) {
                            drawLine(
                                Color.LightGray,
                                start = Offset(position.x, 0f),
                                end = Offset(position.x, size.height.toFloat())
                            )
                            drawLine(
                                Color.LightGray,
                                start = Offset(0f, position.y),
                                end = Offset(size.width.toFloat(), position.y)
                            )
                        }
                    }

                    Column( modifier = Modifier.fillMaxSize().padding(0.dp, 0.dp, marginX,marginY)) {
                        Spacer(Modifier.weight(1f))
                        Row() {
                            Spacer(Modifier.weight(1f))
                            BlisterTemplateView(viewModel)
                        }
                    }
                }
            })
}

fun save(viewModel: CalibrationViewModel, appViewModel: AppViewModel) {
    viewModel.save()
    appViewModel.navigation.pop()
}

fun cancel(viewModel: CalibrationViewModel, appViewModel: AppViewModel) {
    appViewModel.navigation.pop()
}

