package de.fishbyte.mrblister.app

import de.fishbyte.mrblister.services.SearchService
import de.fishbyte.mrblister.app.theme.AppTheme
import de.fishbyte.mrblister.app.theme.DefaultAppTheme
import de.fishbyte.mrblister.app.views.AppNavigation
import org.koin.core.module.dsl.bind
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.module
import de.fishbyte.mrblister.model.SettingsImpl
import de.fishbyte.mrblister.model.UserRepository
import org.koin.core.module.Module

fun appModule( configPath: String) : Module {

    val appModule = module {
        // Define your dependencies here
        singleOf(::DefaultAppTheme) { bind<AppTheme>() }
        singleOf(::SettingsImpl) { bind<Settings>() }
        single { UserRepository() }
        single { AppNavigation() }
        single { SearchService(get()) }

        single { AppViewModel( getKoin().get(), getKoin().get(), getKoin().get(), configPath ) }
    }
    return appModule
}
