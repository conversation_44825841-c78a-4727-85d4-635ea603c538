package de.fishbyte.mrblister.app.views

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.util.VersionInfo

@Composable
fun AppVersionInfo() {
    Column(modifier = Modifier.padding(16.dp)) {
        Text(
            text = "MrBlister v1.0.${VersionInfo.buildNumber}",
            style = MaterialTheme.typography.bodyMedium
        )
    }
}
