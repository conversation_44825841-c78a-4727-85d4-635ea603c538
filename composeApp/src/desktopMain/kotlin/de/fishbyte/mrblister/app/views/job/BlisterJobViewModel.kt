package de.fishbyte.mrblister.app.views.job

import Dialog
import androidx.compose.ui.input.key.Key
import de.fishbyte.mrblister.app.AppViewModel
import de.fishbyte.mrblister.app.PicturesSetting
import de.fishbyte.mrblister.app.camera.AutoConfirmable
import de.fishbyte.mrblister.app.camera.CameraViewModel
import de.fishbyte.mrblister.app.camera.CameraViewState
import de.fishbyte.mrblister.app.matchBarcode
import de.fishbyte.mrblister.app.views.KeyboardHandler
import de.fishbyte.mrblister.app.views.joblist.JobListViewModel
import de.fishbyte.mrblister.app.views.tools.Barcode
import de.fishbyte.mrblister.app.views.tools.formatShortDateTime
import de.fishbyte.mrblister.app.views.tools.name
import de.fishbyte.mrblister.model.*
import de.fishbyte.mrblister.services.LoggerService
import de.fishbyte.mrblister.services.SearchService
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.awt.Color
import java.awt.Image
import java.awt.image.BufferedImage
import java.io.File
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.WeekFields
import java.util.stream.Collectors
import javax.imageio.ImageIO
import kotlin.math.floor

enum class BlisterJobViewModelStatus {
    Active,
    TakeMedicationPicture,
    TakeJobPicture,
    Finished
}

enum class ToastStatus {
    Success,
    Error,
    Info
}

class Toast(val message: String, val status: ToastStatus, duration: Int = 10) {
    var timer: Int = duration*10
}

class BlisterJobViewModel(val job: BlisterJob, val appViewModel: AppViewModel, val jobGroup: String?, val logger: LoggerService, private val searchService: SearchService) : MedicationGridViewModel(),
    KeyboardHandler
{
    private val _status = MutableStateFlow(BlisterJobViewModelStatus.Active)
    val status: StateFlow<BlisterJobViewModelStatus> get() = _status.asStateFlow()


    private val _medicationStatus = MutableStateFlow(MedicationStatus.Pending)
    val medicationStatus: StateFlow<MedicationStatus> get() = _medicationStatus.asStateFlow()

    private val _medications = MutableStateFlow<List<MedicationSummary>>(emptyList())
    val medications: StateFlow<List<MedicationSummary>> get() = _medications.asStateFlow()

    private val _cameraViewModel = MutableStateFlow(CameraViewModel(
        appViewModel.settings.cameraCountDown,
        object : AutoConfirmable {
            override fun acceptPicture() {
                onNext()
            }
        }
    ))
    val cameraViewModel: StateFlow<CameraViewModel> = _cameraViewModel.asStateFlow()

    private val _toast = MutableStateFlow<Toast?>(null)
    val toast: StateFlow<Toast?> get() = _toast.asStateFlow()

    private val _banner = MutableStateFlow<String?>(null)
    val banner: StateFlow<String?> get() = _banner.asStateFlow()

    var jobStatus: JobStatus


    private var _nextMedication: MedicationSummary? = null

    init {
        val drugIDs = mutableSetOf<String>()

        val status = appViewModel.findJobStatus(job.localID)
        jobStatus = status?.status ?: JobStatus.Pending

        this.job.medications.stream().map { it.drugID }.forEach { drugIDs.add(it) }

        _medications.value = drugIDs.stream().map { drugID ->
            val drug = drugById(drugID) ?: throw RuntimeException("Drug not found")
            val medications = this.job.medications.filter { it.drugID == drugID }.toList()
            val dosage = medications.sumOf { it.dosage }

            MedicationSummary(drug, medications, dosage, status?.getMedicationStatus(drugID)?.status ?: MedicationStatus.Pending)
        }.toList()
    }

    private fun takingColumnIndex( column: String?, jobType: String ) : Int
    {
        if( column.isNullOrBlank() ) {
            return -1
        }
        val startRow = when(jobType) {
            "WeekA4", "WeekB4" -> -1
            else -> 0
        }

        return when(column) {
            "Sober", "Column1" -> startRow + 0
            "Morning", "Column2" -> startRow + 1
            "Noon", "Column3" -> startRow + 2
            "Evening", "Column4" -> startRow + 3
            "Night", "Column5" -> startRow + 4
            "Column6" -> startRow + 5
            "Column7" -> startRow + 6
            else -> -1
        }
    }

    fun start()
    {
        val config = getBlisterConfig(job)
        start(job.getTakingDates(config),config,this.job.medications.stream().map { medication -> Pair(medication.takingTime, takingColumnIndex(medication.takingColumn,job.type)) }.toList())
    }

    private fun getBlisterConfig(job: BlisterJob): BlisterConfiguration {

        // first check station and name
        if( job.patient.nursingHome != null ) {
            val nameAndStation = job.patient.nursingHome.getNameAndStation()
            val candidate = appViewModel.blisterConfiguration.find { it.stations.contains(nameAndStation) }
            if (candidate != null) {
                return candidate
            }
        }

        // fall back to type
        return appViewModel.blisterConfiguration.find { it.name == job.type } ?: appViewModel.blisterConfiguration.first()
    }

    override fun handleBarcode(key: String) {

        val decoded = Barcode.parse(key)

        val pzn = decoded.pzn

        val isExpired = decoded.expiryDate?.isBefore(job.getLatestDate()) ?: false
        if (isExpired) {
            val exp = decoded.expiryDate?.format(DateTimeFormatter.ofPattern("dd.MM.yyyy"))
            logger.info("Abgelaufenes Medikament aufgenommen, PZN $pzn, Exp: $exp")
            appViewModel.showAlert("Das Medikament mit PZN $pzn ist am $exp abgelaufen!")
            return
        }

        val medication = if( pzn != null ) medications.value.stream().filter { med ->
            med.drug.packingTypes?.packingTypeList?.stream()?.anyMatch { it.id.matchBarcode(pzn) } == true
        }.findFirst().orElse(null) else null

        if( medication != null ) {
            if( _medication.value?.drug?.id == medication.drug.id ) {
                // medication already active
                _toast.value = Toast("Aktuelles Medikament gescannt!", ToastStatus.Success)
                return
            }

            if( _status.value == BlisterJobViewModelStatus.TakeMedicationPicture && _cameraViewModel.value.state.value == CameraViewState.captured ) {
                _nextMedication = medication
                _cameraViewModel.value.stopAutoConfirm()
                finishMedication()
                return
            }

            if( _medication.value != null ) {
                _toast.value = Toast("Falsches Medikament gescannt!", ToastStatus.Error)
                return
            }

            startMedication(medication.drug)
        } else {

            if( pzn != null && job.patient.id.matchBarcode(pzn) ) {
                if( _status.value == BlisterJobViewModelStatus.TakeJobPicture ) {

                    return
                }
            }
            logger.info("Unbekannter Medikament Barcode: $key")
            appViewModel.showAlert("Kein Medikament mit der PZN $pzn gefunden")
        }
    }

    override fun handleActionKey(key: Long) {
        when( key ) {
            Key.Escape.keyCode -> {
                when (status.value) {
                    BlisterJobViewModelStatus.Active -> {
                        when (medicationStatus.value) {
                            MedicationStatus.Pending -> close()
                            else -> {}
                        }
                    }

                    BlisterJobViewModelStatus.TakeMedicationPicture,
                    BlisterJobViewModelStatus.TakeJobPicture -> {
                        // TODO
                    }

                    BlisterJobViewModelStatus.Finished -> {
                        close()
                    }

                }
            }
            Key.Spacebar.keyCode -> {

                onNext()
            }
        }
    }

    fun onNext()
    {
        when (status.value) {
            BlisterJobViewModelStatus.Active -> {
                when (medicationStatus.value) {
                    MedicationStatus.InProgress -> takePictureOfMedication()
                    else -> finishMedication()
                }
            }

            BlisterJobViewModelStatus.TakeMedicationPicture,
            BlisterJobViewModelStatus.TakeJobPicture -> {
                acceptPicture()
            }

            BlisterJobViewModelStatus.Finished -> {
                close()
            }

        }
    }

    fun acceptPicture() {
        val viewModel = cameraViewModel.value
        if( viewModel.state.value == CameraViewState.captured || viewModel.state.value == CameraViewState.rolling ) {
            viewModel.currentImage.value?.let {
                if (status.value == BlisterJobViewModelStatus.TakeMedicationPicture) {
                    acceptMedicationPicture(it)
                } else {
                    acceptJobPicture(it)
                }
            }
        }
    }

    private fun drugById(drugID: String): Drug? {
        return job.drugs.stream().filter { it.id == drugID }.findFirst().orElse(null)
    }

    private fun updateMedicationStatus( status: MedicationStatus, reason: String = "" ) {
        _medication.value?.let { medication ->
            medication.status = status
            val jobStatus = appViewModel.getOrCreateJobStatus(job)
            jobStatus.getMedicationStatus(medication.drug.id).apply {
                this.status = status
                this.cancelReason = reason
            }

            appViewModel.write(jobStatus)
        }
    }

    private fun getSummary(drug: Drug) : MedicationSummary?
    {
        val summary =
            medications.value.stream().filter { it.drug.id == drug.id }.findFirst().orElse(null)

        if( summary == null ) {
            appViewModel.showAlert("Medikament ${drug.name} nicht gefunden")
            return null
        }
/*
        if( summary.status == MedicationStatus.Completed ) {
            appViewModel.showAlert("Medikament ${drug.name} bereits abgeschlossen")
            return null
        }
*/
        return summary
    }

    fun startMedicationManual(drug: Drug) {
        val summary = getSummary(drug) ?: return

        if( appViewModel.settings.medicationScanNag ) {
            appViewModel.showDialog {
                Dialog("Manuell starten",
                    "Es muss die Packung per Barcode gescannt werden. Nur in Ausnahmefällen ist eine manuelle Auswahl erlaubt.",

                    onCloseMessage = "zurück zum Scannen der Packung",
                    onClose = {
                        appViewModel.dismissDialog()
                    },
                    onConfirmMessage = "Packung manuell auswählen",
                    onConfirm = {
                        logger.info("Manuell Medikament ausgewählt: ${drug.name}")
                        startMedicationManualImpl(summary)
                    })
            }
        } else {
            logger.info("Manuell Medikament ausgewählt: ${drug.name}")
            startMedicationManualImpl(summary)
        }
    }

    private fun startMedicationManualImpl(summary: MedicationSummary)
    {
        val medications = _medications.value.flatMap { it.medications }
        val firstDate = medications.minByOrNull { it.takingDate }?.takingDate
        val calendarWeek = firstDate?.get(WeekFields.ISO.weekOfWeekBasedYear())

        logger.info("Medikament ${summary.drug.name} für Patient ${job.patient.id} / ${job.patient.nursingHome?.name ?: "-"} / KW $calendarWeek wurde manuell gestartet")
        startMedicationImpl(summary)
    }

    private fun startMedication(drug: Drug) {
        val summary = getSummary(drug) ?: return
        startMedicationImpl(summary)
    }

    private fun startMedicationImpl(summary: MedicationSummary)
    {
        if (summary.status == MedicationStatus.Completed) {
            appViewModel.showDialog {
                Dialog("Medikament bereits abgeschlossen",
                    "Das Medikament ${summary.drug.name} wurde bereits abgeschlossen. Soll es erneut gestellt werden?",
                    onCloseMessage = "Abbrechen",
                    onClose = {
                        appViewModel.dismissDialog()
                    },
                    onConfirmMessage = "Erneut stellen",
                    onConfirm = {
                        startMedicationImpl2(summary)
                    }
                )
            }
        } else {
            startMedicationImpl2(summary)
        }
    }
    private fun startMedicationImpl2(summary: MedicationSummary)
    {
        if( medicationStatus.value == MedicationStatus.InProgress ) {
            _nextMedication = summary
            finishMedication()
        } else {
            startMedication(summary)
        }
    }

    private fun startMedication(summary: MedicationSummary)
    {
        val medicationInfo = summary.medications.stream().map { it.information }.filter { !it.isNullOrBlank() }.distinct()
            .collect(Collectors.joining(";"))

        if(medicationInfo?.lowercase()?.contains("blister") == true) {
            _banner.value = medicationInfo
        } else {
            _banner.value = null
        }

        _medication.value = summary

        updateMedicationStatus(MedicationStatus.InProgress)
        _medicationStatus.value = MedicationStatus.InProgress

        if( jobStatus == JobStatus.Pending || jobStatus == JobStatus.Paused ) {
            updateJobStatus(JobStatus.InProgress)
        }
    }

    /** single medication is completed */
    fun finishMedication() {

        _cameraViewModel.value?.let {
            it.stopAutoConfirm()
        }

        updateMedicationStatus(MedicationStatus.Completed)

        dismissToast()

        _medication.value = null
        _medicationStatus.value = MedicationStatus.Pending

        if( _nextMedication != null ) {
            val drug = _nextMedication!!.drug
            _nextMedication = null
            _status.value = BlisterJobViewModelStatus.Active
            startMedication(drug)
            return
        }

        if( medications.value.all { it.status == MedicationStatus.Completed } ) {

            // all medications are finished, final picture or job complete
            if( _status.value != BlisterJobViewModelStatus.TakeJobPicture && ( appViewModel.settings.picture == PicturesSetting.JOB || appViewModel.settings.picture == PicturesSetting.ALL ) ) {
                _status.value = BlisterJobViewModelStatus.TakeJobPicture
                _cameraViewModel.value.setRollingMode( true)
                _toast.value = Toast("Kontrollfoto für Blister erstellen", ToastStatus.Info, 0)
            } else {
                _status.value = BlisterJobViewModelStatus.Finished
                finishJob()
            }
        }
        else {
            _status.value = BlisterJobViewModelStatus.Active
        }
    }

    fun takePictureOfMedication() {
        if( appViewModel.settings.picture == PicturesSetting.MEDICATION || appViewModel.settings.picture == PicturesSetting.ALL ) {
            _status.value = BlisterJobViewModelStatus.TakeMedicationPicture
            _cameraViewModel.value.setRollingMode(false)
            _toast.value = Toast("Foto von Medikation wird erstellt...", ToastStatus.Info, 0)
        } else {
            finishMedication()
        }
    }

    fun present(appViewModel: AppViewModel) {
        appViewModel.navigation.push(this)
    }

    private fun updateJobStatus( status: JobStatus ) {
        jobStatus = status
        val jobStatus = appViewModel.getOrCreateJobStatus(job)
        jobStatus.status = status
        appViewModel.write(jobStatus)
        if( status == JobStatus.Completed ) {
            appViewModel.archive(job, searchService)
        }
    }

    private fun finishJob() {
        _status.value = BlisterJobViewModelStatus.Finished
        updateJobStatus(JobStatus.Completed)
        close()
    }

    fun close()
    {
        JobListViewModel.present(appViewModel, jobGroup)
    }

    private fun acceptMedicationPicture(image: BufferedImage) {
        persistImage2(image, "image_${job.localID}_${medication.value?.drug?.id}.png")
        finishMedication()
    }

    private fun acceptJobPicture(image: BufferedImage) {
        persistImage(image, "image_${job.localID}.png")
        finishJob()
    }

    private fun persistImage2(image: BufferedImage, path: String) {
        try {
            val font = java.awt.Font("Arial", java.awt.Font.PLAIN, 20)
            val lineHeight = font.size2D * 1.2f // 20% spacing between lines
            val extraHeight = (lineHeight * 4).toInt() // Space for 4 lines
            val borderThickness = floor(lineHeight / 4).toInt()// 25% border thickness

            val width = 1920;
            val height = image.height * 1920 / image.width


            val newImage = BufferedImage(
                width,
                height + extraHeight + borderThickness,
                BufferedImage.TYPE_INT_RGB
            )

            val g2d = newImage.createGraphics()
            g2d.drawImage(image, 0, 0, width, height, null)

            // Fill bottom area with white
            g2d.color = Color.WHITE
            g2d.fillRect(0, height, width, extraHeight + borderThickness)

            // Draw bottom border
            g2d.color = Color.BLACK
            g2d.fillRect(0, height, width, borderThickness)

            // Configure text rendering
            g2d.color = Color.BLACK
            g2d.font = font

            val columnWidth = width / 3

            // Column 1 - Patient info
            val col1X = 12
            val col1Texts = listOf(
                name(job.patient),
                "Geb: ${job.patient.birthday.format(DateTimeFormatter.ofPattern("dd.MM.yyyy"))}",
                "Medikament: ${medication.value?.drug?.name}"
            )

            // Column 2 - Blister info
            val col2X = columnWidth
            val col2Texts = listOf(
                "Blisterkraft: ${appViewModel.user.value?.name ?: '-'}"
            )

            // Column 3 - Additional info
            val col3X = columnWidth * 2
            val col3Texts = listOf(
                "Erstellt: ${formatShortDateTime(LocalDateTime.now())}",
                job.patient.nursingHome?.name ?: ""
            )

            // Render all columns
            listOf(
                col1Texts to col1X,
                col2Texts to col2X,
                col3Texts to col3X
            ).forEach { (texts, x) ->
                texts.forEachIndexed { index, text ->
                    val y = height + borderThickness + (lineHeight * (index + 1)).toInt()
                    g2d.drawString(text, x, y)
                }
            }

            g2d.dispose()

            val file = File(appViewModel.imageStoragePath(), path)
            file.parentFile.mkdirs()
            ImageIO.write(newImage, "PNG", file)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun persistImage(image: BufferedImage, path: String) {
        val font = java.awt.Font("Arial", java.awt.Font.PLAIN, 20)
        val lineHeight = font.size2D * 1.2f // 20% spacing between lines
        val extraHeight = (lineHeight * 4).toInt() // Space for 4 lines
        val borderThickness = floor(lineHeight / 4).toInt()// 25% border thickness

        val width = 1920;
        val height = image.height * 1920 / image.width


        val newImage = BufferedImage(
            width,
            height + extraHeight + borderThickness,
            BufferedImage.TYPE_INT_RGB
        )

        val g2d = newImage.createGraphics()
        g2d.drawImage(image, 0, 0, width, height, null)

        // Fill bottom area with white
        g2d.color = Color.WHITE
        g2d.fillRect(0, height, width, extraHeight + borderThickness)

        // Draw bottom border
        g2d.color = Color.BLACK
        g2d.fillRect(0, height, width, borderThickness)

        // Configure text rendering
        g2d.color = Color.BLACK
        g2d.font = font

        val columnWidth = width / 3

        // Column 1 - Patient info
        val col1X = 12
        val col1Texts = listOf(
            name(job.patient),
            "Geb: ${job.patient.birthday.format(DateTimeFormatter.ofPattern("dd.MM.yyyy"))}",
            "Job: ${job.id}"
        )

        // Column 2 - Blister info
        val col2X = columnWidth
        val col2Texts = listOf(
            "Blisterkraft: ${appViewModel.user.value?.name ?: '-'}",
            "Order: ${job.blisterOrderID}"
        )

        // Column 3 - Additional info
        val col3X = columnWidth * 2
        val col3Texts = listOf(
            "Erstellt: ${formatShortDateTime(LocalDateTime.now())}",
            "Patient: ${job.patient.id}",
            job.patient.nursingHome?.name ?: ""
        )

        // Render all columns
        listOf(
            col1Texts to col1X,
            col2Texts to col2X,
            col3Texts to col3X
        ).forEach { (texts, x) ->
            texts.forEachIndexed { index, text ->
                val y = height + borderThickness + (lineHeight * (index + 1)).toInt()
                g2d.drawString(text, x, y)
            }
        }

        g2d.dispose()

        try {
            val file = File( appViewModel.imageStoragePath(), path )
            file.parentFile.mkdirs()
            ImageIO.write(newImage, "PNG", file)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun cancelMedication( reason: String) {

        if( _medication.value != null ) {
            logger.info("Blistern ${job.id}, Medikation ${medication.value?.drug?.id} abgebrochen, Grund: $reason")

            updateMedicationStatus(MedicationStatus.Pending, reason)
            _medicationStatus.value = MedicationStatus.Pending
            _medication.value = null
        } else {
            logger.info("Blistern ${job.id} abgebrochen, Grund: $reason")
            val jobStatus = appViewModel.getOrCreateJobStatus(job)
            if( jobStatus.isPending() ) {

                this.jobStatus = JobStatus.Pending
                if (jobStatus.status != JobStatus.Pending) {
                    jobStatus.status = JobStatus.Pending
                    appViewModel.write(jobStatus)
                }
            }
        }
    }

    fun updateToast() {
        if( _toast.value != null && _toast.value!!.timer > 0 ) {
            _toast.value!!.timer -= 1
            if( _toast.value!!.timer <= 0 ) {
                _toast.value = null
            }
        }
    }

    private fun dismissToast() {
        _toast.value = null
    }
}

