package de.fishbyte.mrblister.app.views.usermanagement

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import de.fishbyte.mrblister.app.AppViewModel
import de.fishbyte.mrblister.app.views.KeyboardHandler
import de.fishbyte.mrblister.model.User
import de.fishbyte.mrblister.model.UserRepository
import de.fishbyte.mrblister.model.UserRole
import de.fishbyte.mrblister.tools.generateUniqueId
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class UserManagementViewModel(val appViewModel: AppViewModel) : ViewModel(), KeyboardHandler {
    private val _users = MutableStateFlow<List<User>>(emptyList())
    val users: StateFlow<List<User>> get() = _users.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> get() = _errorMessage.asStateFlow()

    private val _successMessage = MutableStateFlow<String?>(null)
    val successMessage: StateFlow<String?> get() = _successMessage.asStateFlow()

    // New user form fields
    private val _newUserName = MutableStateFlow("")
    val newUserName: StateFlow<String> get() = _newUserName.asStateFlow()

    private val _newUserPassword = MutableStateFlow("")
    val newUserPassword: StateFlow<String> get() = _newUserPassword.asStateFlow()

    private val _newUserRole = MutableStateFlow(UserRole.USER)
    val newUserRole: StateFlow<UserRole> get() = _newUserRole.asStateFlow()

    // Selected user for operations
    private val _selectedUser = MutableStateFlow<User?>(null)
    val selectedUser: StateFlow<User?> get() = _selectedUser.asStateFlow()

    // Password change fields
    private val _newPassword = MutableStateFlow("")
    val newPassword: StateFlow<String> get() = _newPassword.asStateFlow()

    private val _confirmPassword = MutableStateFlow("")
    val confirmPassword: StateFlow<String> get() = _confirmPassword.asStateFlow()

    init {
        refreshUsers()
    }

    private fun refreshUsers() {
        _users.value = appViewModel.userRepository.getUsers()
    }

    fun updateNewUserName(name: String) {
        _newUserName.value = name
        _errorMessage.value = null
    }

    fun updateNewUserPassword(password: String) {
        _newUserPassword.value = password
        _errorMessage.value = null
    }

    fun updateNewUserRole(role: UserRole) {
        _newUserRole.value = role
        _errorMessage.value = null
    }

    fun selectUser(user: User) {
        _selectedUser.value = user
        _errorMessage.value = null
    }

    fun updateNewPassword(password: String) {
        _newPassword.value = password
        _errorMessage.value = null
    }

    fun updateConfirmPassword(password: String) {
        _confirmPassword.value = password
        _errorMessage.value = null
    }

    fun clearNewUserForm() {
        _newUserName.value = ""
        _newUserPassword.value = ""
        _newUserRole.value = UserRole.USER
        _errorMessage.value = null
    }

    fun clearPasswordForm() {
        _newPassword.value = ""
        _confirmPassword.value = ""
        _errorMessage.value = null
    }

    fun addUser() {
        viewModelScope.launch {
            try {
                // Validate input
                if (newUserName.value.isBlank()) {
                    _errorMessage.value = "Benutzername darf nicht leer sein"
                    return@launch
                }

                if (newUserPassword.value.isBlank()) {
                    _errorMessage.value = "Passwort darf nicht leer sein"
                    return@launch
                }

                // Check if user already exists
                if (appViewModel.userRepository.getUserByName(newUserName.value) != null) {
                    _errorMessage.value = "Ein Benutzer mit diesem Namen existiert bereits"
                    return@launch
                }

                // Create new user
                val newUser = User(
                    name = newUserName.value,
                    pinHash = UserRepository.hashPassword(newUserPassword.value),
                    role = newUserRole.value
                )

                // Add user to repository
                appViewModel.userRepository.addUser(newUser)
                appViewModel.userRepository.saveUsers()

                // Clear form and refresh user list
                clearNewUserForm()
                refreshUsers()
                _successMessage.value = "Benutzer ${newUser.name} erfolgreich hinzugefügt"
            } catch (e: Exception) {
                _errorMessage.value = "Fehler beim Hinzufügen des Benutzers: ${e.message}"
            }
        }
    }

    override fun handleBarcode(key: String) {
    }

    fun removeUser(user: User) {
        viewModelScope.launch {
            try {
                // Check if user is the current logged-in user
                if (appViewModel.user.value == user) {
                    _errorMessage.value = "Der aktuell angemeldete Benutzer kann nicht gelöscht werden"
                    return@launch
                }

                // Remove user from repository
                appViewModel.userRepository.removeUser(user)
                appViewModel.userRepository.saveUsers()

                // Refresh user list
                refreshUsers()
                _successMessage.value = "Benutzer ${user.name} erfolgreich gelöscht"
            } catch (e: Exception) {
                _errorMessage.value = "Fehler beim Löschen des Benutzers: ${e.message}"
            }
        }
    }

    fun changePassword() {
        viewModelScope.launch {
            try {
                val user = selectedUser.value ?: return@launch

                // Validate input
                if (newPassword.value.isBlank()) {
                    _errorMessage.value = "Neues Passwort darf nicht leer sein"
                    return@launch
                }

                if (newPassword.value != confirmPassword.value) {
                    _errorMessage.value = "Passwörter stimmen nicht überein"
                    return@launch
                }

                // Change password
                appViewModel.userRepository.changePassword(user, newPassword.value)
                appViewModel.userRepository.saveUsers()

                // Clear form and refresh user list
                clearPasswordForm()
                refreshUsers()
                _selectedUser.value = null
                _successMessage.value = "Passwort für ${user.name} erfolgreich geändert"
            } catch (e: Exception) {
                _errorMessage.value = "Fehler beim Ändern des Passworts: ${e.message}"
            }
        }
    }

    fun dismissErrorMessage() {
        _errorMessage.value = null
    }

    fun dismissSuccessMessage() {
        _successMessage.value = null
    }

    override fun handleActionKey(key: Long) {
        // Not needed for this view
    }

    companion object {
        fun present(appViewModel: AppViewModel) {
            appViewModel.navigation.push(UserManagementViewModel(appViewModel))
        }
    }
}
