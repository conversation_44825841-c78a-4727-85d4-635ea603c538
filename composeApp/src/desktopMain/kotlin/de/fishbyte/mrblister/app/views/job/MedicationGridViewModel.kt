package de.fishbyte.mrblister.app.views.job

import androidx.lifecycle.ViewModel
import de.fishbyte.mrblister.model.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.time.LocalDate
import java.time.LocalTime
import java.util.stream.Collectors

open class MedicationGridViewModel : ViewModel() {

    protected val _medication = MutableStateFlow<MedicationSummary?>(null)
    val medication: StateFlow<MedicationSummary?> get() = _medication.asStateFlow()

    var blisterConfig : BlisterConfiguration? = null

    private val _firstColor = MutableStateFlow(true)
    val firstColor: StateFlow<Boolean> = _firstColor.asStateFlow()

    private var _takingDates : List<LocalDate> = emptyList()
    fun getTakingDates() : List<LocalDate> {
        return _takingDates
    }

    fun toggleFirstColor() {
        _firstColor.value = !_firstColor.value
    }

    protected var _rows: List<LocalTime?>
    val rows: List<LocalTime?>
        get() {
            return _rows
        }

    protected var _rowsUnsorted: List<LocalTime?>
    val rowsUnsorted: List<LocalTime?>
        get() {
            return _rowsUnsorted
        }

    init {
        _rows = emptyList()
        _rowsUnsorted = emptyList()
    }

    fun start( takingDates: List<LocalDate>, blisterConfig: BlisterConfiguration, takingTimes : List<Pair<LocalTime, Int>> )
    {
        _takingDates = takingDates
        this.blisterConfig = blisterConfig

        val numberOfRows = blisterConfig.rowHeight?.size ?: 5

        // Create array to hold times at specific row indices
        val rowArray = arrayOfNulls<LocalTime>(numberOfRows)
        
        // Place distinct times at their specified row indices
        takingTimes.distinctBy { it.first }.forEach { (time, rowIndex) ->
            if (rowIndex < numberOfRows) {
                rowArray[rowIndex] = time
            }
        }

        /*
        // Find first and last non-null indices
        val firstNonNull = rowArray.indexOfFirst { it != null }
        val lastNonNull = rowArray.indexOfLast { it != null }
        
        // Extract sublist from first to last non-null (inclusive)
        val trimmedRows = if (firstNonNull != -1 && lastNonNull != -1) {
            rowArray.slice(firstNonNull..lastNonNull).toMutableList()
        } else {
            emptyList<LocalTime?>().toMutableList()
        }
        while(trimmedRows.size < numberOfRows ) {
            if( blisterConfig.flipTime == true ) {
                trimmedRows.add(0, null)
            } else {
                trimmedRows.add(null)
            }
        }

         */


        _rowsUnsorted = rowArray.toList()

        val rows = rowArray.toMutableList()
        if( blisterConfig?.flipTime == true ) {
            rows.reverse()
        }

        _rows = rows.toList()
    }
}