package de.fishbyte.mrblister.app.views.joblist

import androidx.lifecycle.ViewModel
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import de.fishbyte.mrblister.app.*
import de.fishbyte.mrblister.app.views.KeyboardHandler
import de.fishbyte.mrblister.app.views.job.BlisterJobViewModel
import de.fishbyte.mrblister.app.views.tools.name
import de.fishbyte.mrblister.model.*
import de.fishbyte.mrblister.services.LoggerService
import de.fishbyte.mrblister.services.SearchService
import de.fishbyte.mrblister.tools.generateUniqueId
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.File

class JobListViewModel( val appViewModel: AppViewModel) : ViewModel(), KeyboardHandler {
    private val _state = MutableStateFlow<ViewState>(LoadingViewState)
    val state: StateFlow<ViewState> get() = _state.asStateFlow()

    private val _group = MutableStateFlow<String?>(null)
    val group: StateFlow<String?> get() = _group.asStateFlow()


    fun startJob(job: BlisterJobViewModel) {
        job.start()
        job.present(appViewModel)
    }

    companion object {
        fun present(appViewModel: AppViewModel, group: String? = null) {
            val model = JobListViewModel(appViewModel)
            if( group != null ) {
                model._group.value = group
            }
            appViewModel.navigation.replaceAll(model)
        }
    }

    override fun handleBarcode(barcode: String) {

        val jobs = appViewModel.jobs.value
        var found = ""
        for (job in jobs) {
            if ( job.job.patient.id == barcode ) {

                if( job.jobStatus == JobStatus.Completed ) {
                    found = name(job.job.patient)
                    continue
                } else {
                    startJob(job)
                }
                return
            }
        }

        if( found.isNotEmpty() ) {
            appViewModel.showAlert("Der Patient ${found} wurde schon bearbeitet")
        }
        appViewModel.showAlert("Kein Patient mit dem gescannten Barcode ${barcode} gefunden")
    }

    fun selectGroup(nursingHome: NursingHome?) {
        _group.value = nursingHome.getNameAndStation()
    }
}

fun AppViewModel.importBlisterFiles(jobFiles: Set<File>, logger: LoggerService, searchService: SearchService) {
    val targetPath = blisterStoragePath()

    if( !targetPath.exists() ) {
        targetPath.mkdirs()
    }

    val importsStoragePath = importsStoragePath()

    val jobs1 = jobs.value.toMutableList()
    val blister1 = blisterJobs.value.toMutableMap()

    val objectMapper = ObjectMapper().registerKotlinModule()
    objectMapper.registerModule(JavaTimeModule())
    objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL)

    for( jobFile in jobFiles ) {

        try {
            val blisterOrder = loadBlisterOrder(jobFile.path)
            for (patient in blisterOrder.patients.patientList) {

                val sortedJobs = patient.jobs.jobList.sortedWith { left, right -> compareJobs(left, patient, right, patient) }

                val targetPatient =  BlisterPatient(
                    id = patient.id,
                    title = patient.title,
                    name1 = patient.name1,
                    name2 = patient.name2,
                    fotoData = patient.fotoData,
                    fotoFile = patient.fotoFile,
                    birthday = patient.birthday,
                    insuranceID = patient.insuranceID,
                    healthInsuranceID = patient.healthInsuranceID,
                    healthInsuranceName = patient.healthInsuranceName,
                    address = patient.address,
                    nursingHome = patient.nursingHome
                )

                for (job in patient.jobs.jobList) {

                    val localID = generateUniqueId()

                    val drugs = job.medications.medicationList.map {
                        it.drugID
                    }.toSet()

                    val targetDrugs = blisterOrder.drugs.drugList.filter { it.id in drugs }.map { it.copy() }

                    val doctors = job.medications.medicationList.map {
                        it.doctorID
                    }

                    val targetDoctors = blisterOrder.doctors.doctorList.filter { it.id in doctors }.map { it.copy() }

                    val indexOf = if(sortedJobs.size > 1) {
                        sortedJobs.size
                    } else {
                        null
                    }

                    val index = if(sortedJobs.size > 1) {
                        sortedJobs.indexOf(job) + 1
                    } else {
                        null
                    }

                    val targetJob = BlisterJob(
                        id = job.id,
                        localID = localID,
                        blisterOrderID = blisterOrder.id,
                        createDate = blisterOrder.createDate,
                        createTime = blisterOrder.createTime,
                        blisterCardID = job.blistercardID,
                        type = job.type,
                        patient = targetPatient.copy(),
                        medications = job.medications.medicationList,
                        drugs = targetDrugs,
                        doctors = targetDoctors,
                        pharmacy = blisterOrder.pharmacy,
                        index = index,
                        indexOf = indexOf
                    )

                    // Serialize targetModel to XML and write to targetFile
                    val targetFile = File(targetPath, targetJob.fileName())

                    objectMapper.writeValue(targetFile, targetJob)


                    jobs1.add( BlisterJobViewModel(targetJob, this, targetPatient.nursingHome.getNameAndStation(), logger, searchService))

                }
            }
            if(settings.moveImports != false ) {
                if( !importsStoragePath.exists() ) {
                    importsStoragePath.mkdirs()
                }
                jobFile.copyTo(File(importsStoragePath, jobFile.name), true)
                jobFile.delete()
            }
        }
        catch (e: Exception) {
            logger.error("Error loading blister file: ${e.message}")
        }
    }

    updateBlisterJobs( jobs1, blister1.toMap() )
}

fun loadBlisterFile(
    xmlFile: File,
    jobs: MutableList<BlisterJobViewModel>,
    appViewModel: AppViewModel,
    logger: LoggerService,
    searchService: SearchService
)
{
    try {

        val blisterJob = BlisterJob.load(xmlFile.path)

        jobs.add( BlisterJobViewModel(blisterJob, appViewModel, blisterJob.patient.nursingHome.getNameAndStation(), logger, searchService))
    } catch (e: Exception) {
        // TODO: Log this
        e.printStackTrace()
        logger.error("Error loading blister file: ${e.message}")
    }
}

fun AppViewModel.loadBlisterJobs(logger: LoggerService, searchService: SearchService) {

    val jobDirectory = blisterStoragePath()
    if( jobDirectory.isDirectory ) {

        val jsonFiles = jobDirectory.listFiles { file ->
            file.isFile && file.name.startsWith("job_") && file.name.endsWith(".job.json")
        }?.toList() ?: emptyList()

        val jobs = mutableListOf<BlisterJobViewModel>()
        val blister = mutableMapOf<String,BlisterOrder>()

        for( jsonFile in jsonFiles ) {
            loadBlisterFile(jsonFile, jobs, this, logger, searchService)
        }

        updateBlisterJobs( jobs, blister )
    } else {
        updateBlisterJobs( emptyList(), emptyMap() )
    }
}
