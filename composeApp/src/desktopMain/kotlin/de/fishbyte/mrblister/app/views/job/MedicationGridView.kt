package de.fishbyte.mrblister.app.views.job

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.material.Text
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.AppViewModel
import de.fishbyte.mrblister.app.theme.AppTheme
import de.fishbyte.mrblister.components.GridWithHeaders
import de.fishbyte.mrblister.model.Medication
import kotlinx.coroutines.delay
import org.koin.compose.getKoin
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.*

/*
private fun getDays(viewModel: MedicationGridViewModel): List<String>
{
val blisterConfiguration = viewModel.blisterConfig
val daysReversed = blisterConfiguration?.flipDays == true

val startDate = viewModel.getStartDate()
val endDate = viewModel.getEndDate()

// Generate day abbreviations for the date range
val dayAbbreviations = mutableListOf<String>()
var currentDate = startDate

while (currentDate != null && (endDate == null || !currentDate.isAfter(endDate))) {
    val dayAbbrev = when (currentDate.dayOfWeek) {
        java.time.DayOfWeek.MONDAY -> "Mo"
        java.time.DayOfWeek.TUESDAY -> "Di"
        java.time.DayOfWeek.WEDNESDAY -> "Mi"
        java.time.DayOfWeek.THURSDAY -> "Do"
        java.time.DayOfWeek.FRIDAY -> "Fr"
        java.time.DayOfWeek.SATURDAY -> "Sa"
        java.time.DayOfWeek.SUNDAY -> "So"
    }
    dayAbbreviations.add(dayAbbrev)
    currentDate = currentDate.plusDays(1)
}

return if (daysReversed) dayAbbreviations.reversed() else dayAbbreviations


}
 */

@Composable
fun MedicationGridView(viewModel: MedicationGridViewModel) {

    val theme = getKoin().get<AppTheme>()
    val appViewModel = getKoin().get<AppViewModel>()

    val medication = viewModel.medication.collectAsState()

    val blisterConfiguration = viewModel.blisterConfig
    val rowHeights = blisterConfiguration?.rowHeight?.map { it.dp } ?: emptyList()
    val columnWidths = blisterConfiguration?.columnWidth?.map { it.dp } ?: emptyList()

    LaunchedEffect(Unit) {
        while(true) {
            delay(1000)
            viewModel.toggleFirstColor()
        }
    }

    val isFirstColor = viewModel.firstColor.collectAsState()

    val medications = medication.value?.medications ?: emptyList()
        Box(modifier = Modifier.background(Color.Black)) {

            val daysReversed = blisterConfiguration?.flipDays == true

            val takingDates = viewModel.getTakingDates().let { dates ->
                if( daysReversed ) {
                    dates.reversed()
                } else {
                    dates
                }
            }


            val columnHeaders = takingDates.map {
                 when (it.dayOfWeek) {
                    java.time.DayOfWeek.MONDAY -> "Mo"
                    java.time.DayOfWeek.TUESDAY -> "Di"
                    java.time.DayOfWeek.WEDNESDAY -> "Mi"
                    java.time.DayOfWeek.THURSDAY -> "Do"
                    java.time.DayOfWeek.FRIDAY -> "Fr"
                    java.time.DayOfWeek.SATURDAY -> "Sa"
                    java.time.DayOfWeek.SUNDAY -> "So"
                    else -> ""
                }
            }.toMutableList().apply {
                while (size < 7) {
                    add("")
                }
            }

            val rows = viewModel.rows

            val grid = createGridData(rows.size, columnHeaders.size)

            medications.forEach { medication ->

                val rowIdx = rows.indexOf(medication.takingTime)
                if (rowIdx < 0 || rowIdx >= rows.size) {
                    throw IllegalStateException("takingRow/takingColumn not matching the blister")
                }
                val columnIdx = takingDates.indexOf(medication.takingDate)

                if (columnIdx < 0 || columnIdx >= takingDates.size) {
                    throw IllegalStateException("takingDate not matching the blister")
                }

                grid[rowIdx][columnIdx] = Optional.of(medication)

            }

            val marginX = blisterConfiguration?.marginX?.dp ?: 0.dp
            val marginY = blisterConfiguration?.marginY?.dp ?: 0.dp

            val formatter = DateTimeFormatter.ofPattern("HH:mm")

            Column(modifier = Modifier.fillMaxSize().padding(0.dp, 0.dp, marginX, marginY)) {
                Spacer(Modifier.weight(1f))
                Row(modifier = Modifier.border(1.dp, Color.Black).align(Alignment.End)) {
                    GridWithHeaders(
                        columnHeaders = columnHeaders,
                        rowHeaders = rows.map { it?.format(formatter) ?: "" },
                        gridContent = grid,
                        rowHeights = rowHeights,
                        columnWidths = columnWidths,
                        item = { item ->
                            if (item.isPresent) {
                                val medication = item.get()
                                val color = getColorForDosage(medication.dosage, isFirstColor.value)
                                Box(modifier = Modifier.fillMaxSize().background(color).padding(8.dp)) {
                                    Text(formatDosage(medication.dosage), textAlign = TextAlign.Center, modifier = Modifier.fillMaxSize().wrapContentSize(Alignment.Center), color = Color.White, fontSize = MaterialTheme.typography.headlineMedium.fontSize)
                                }
                            }
                        }
                    )
                }
            }
        }
}

fun createGridData(rows: Int, columns: Int): MutableList<MutableList<Optional<Medication>>> {
    // Create the outer mutable list (for rows)
    val gridData = MutableList(rows) {
        // For each row, create a mutable list of columns
        MutableList<Optional<Medication>>(columns) {
            Optional.empty()
        }
    }
    return gridData
}
