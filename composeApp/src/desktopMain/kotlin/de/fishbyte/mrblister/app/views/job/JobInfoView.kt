package de.fishbyte.mrblister.app.views.job

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.material.Text
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.theme.AppTheme
import de.fishbyte.mrblister.app.views.tools.formatShortDate
import de.fishbyte.mrblister.app.views.tools.formatShortTimeSpan
import de.fishbyte.mrblister.model.BlisterConfiguration
import de.fishbyte.mrblister.model.BlisterJob
import org.koin.compose.getKoin
import java.time.format.DateTimeFormatter

@Composable
fun JobInfoView(job: BlisterJob, config: BlisterConfiguration? = null) {

    val theme = getKoin().get<AppTheme>()

    val patient = job.patient

    ElevatedCard(
        elevation = CardDefaults.cardElevation(defaultElevation = 6.dp),
        modifier = Modifier.fillMaxWidth()) {


        Column(modifier = Modifier.padding(16.dp)) {
            job.getEarliestDate()?.let { startDate ->
                Row {
                    Text("Zeitraum", modifier = Modifier.weight(1f).padding(8.dp))
                    Text(formatShortTimeSpan(startDate,job.getLatestDate()), modifier = Modifier.weight(3f).padding(8.dp))
                }
            }
            /*
            Row {
                Text("Auftrags-Nr.", modifier = Modifier.weight(1f).padding(8.dp))
                Text(order.id, modifier = Modifier.weight(3f).padding(8.dp))
            }

             */
            Row {
                Text("Job-Nr.", modifier = Modifier.weight(1f).padding(8.dp))
                Text(job.id, modifier = Modifier.weight(3f).padding(8.dp))
            }
            /*
            Row {
                Text("Patient", modifier = Modifier.weight(1f).padding(8.dp))
                Text("${patient.name1} ${patient.name2}", modifier = Modifier.weight(3f).padding(8.dp))
            }
            patient.birthday.let {
                Row {
                    Text("Geburtstag", modifier = Modifier.weight(1f).padding(8.dp))
                    Text(it.format(DateTimeFormatter.ofPattern("dd.MM.yyyy")), modifier = Modifier.weight(3f).padding(8.dp))
                }
            }

             */
            patient.nursingHome?.let { nursingHome ->
                val value: StringBuilder = StringBuilder()
                nursingHome?.name.let {
                    value.append(it)
                }
                nursingHome?.station?.let {
                    if (value.isNotEmpty())
                        value.append(" - ")
                    value.append(it)
                }
                nursingHome?.room?.let {
                    if (value.isNotEmpty())
                        value.append(" - ")
                    value.append(it)
                }

                Row {
                    Text("Pflegeheim", modifier = Modifier.weight(1f).padding(8.dp))
                    Text(value.toString(), modifier = Modifier.weight(3f).padding(8.dp))
                }

            }

            config?.let {
                Row {
                    Text("Blister", modifier = Modifier.weight(1f).padding(8.dp))
                    Text(it.name, modifier = Modifier.weight(3f).padding(8.dp))
                }
            }
        }
    }
}