package de.fishbyte.mrblister.app.views.jobimport

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.Settings
import de.fishbyte.mrblister.app.views.joblist.JobListView
import de.fishbyte.mrblister.app.views.joblist.JobListViewModel
import org.koin.compose.getKoin
import java.io.File

@Composable
fun JobImportView(viewModel: JobImportViewModel) {
    val settings = getKoin().get<Settings>()

    var selectedFiles by remember { mutableStateOf(setOf<File>()) }
    val xmlFiles = remember {
        File(settings.jobsPath)
            .listFiles { file -> file.extension.equals("xml", ignoreCase = true) }
            ?.toList() ?: emptyList()
    }

    Row(modifier = Modifier.fillMaxSize()) {

        Column(
            modifier = Modifier
                .padding(16.dp)
                .border(1.dp, Color.Gray, MaterialTheme.shapes.medium)
                .weight(1f)
                .fillMaxHeight()
                .padding(8.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "Select XML Files to Import",
                style = MaterialTheme.typography.headlineMedium
            )

            LazyColumn(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(xmlFiles) { file ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .selectable(
                                selected = selectedFiles.contains(file),
                                onClick = {
                                    selectedFiles = if (selectedFiles.contains(file)) {
                                        selectedFiles - file
                                    } else {
                                        selectedFiles + file
                                    }
                                }
                            )
                            .padding(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Checkbox(
                            checked = selectedFiles.contains(file),
                            onCheckedChange = null
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(file.name)
                    }
                }
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.End,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Button(
                    onClick = { JobListViewModel.present(viewModel.appViewModel) },
                    modifier = Modifier.padding(end = 8.dp)
                ) {
                    Text("Abbrechen")
                }
                Button(
                    onClick = { viewModel.importJobs(selectedFiles) },
                    enabled = selectedFiles.isNotEmpty()
                ) {
                    Text("Auswahl importieren")
                }
            }
        }
        if( !settings.multiMonitor ) {
            Spacer(modifier = Modifier.width(8.dp))
            Box(modifier = Modifier.width(settings.blisterWidth.dp))
        }
    }
}
