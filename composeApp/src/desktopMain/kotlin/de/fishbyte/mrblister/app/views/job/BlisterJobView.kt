package de.fishbyte.mrblister.app.views.job

import de.fishbyte.mrblister.app.camera.cameraView
import Dialog
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.Text
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.camera.cameraViewFooterElements
import de.fishbyte.mrblister.app.Settings
import de.fishbyte.mrblister.app.theme.AppTheme
import de.fishbyte.mrblister.components.Components
import de.fishbyte.mrblister.components.RootSplitViewComponent
import de.fishbyte.mrblister.model.MedicationStatus
import kotlinx.coroutines.delay
import org.koin.mp.KoinPlatform.getKoin
import de.fishbyte.mrblister.components.SelectionView

/**
 * A composable view that handles the blister job workflow.
 *
 * This view manages the medication selection, camera interactions, and job status updates.
 * It displays a split layout with medication controls on the left and a blister preview on the right.
 * The view handles different states including:
 * - Medication selection and scanning
 * - Taking pictures of medications and jobs
 * - Displaying job progress and status
 * - Toast notifications for user feedback
 *F
 * @param viewModel The BlisterJobViewModel that contains the business logic and state
 */
@Composable
fun BlisterJobView(viewModel: BlisterJobViewModel)
{
    val theme = getKoin().get<AppTheme>()
    val medication = viewModel.medication.collectAsState()
    val medicationStatus = viewModel.medicationStatus.collectAsState()
    val status = viewModel.status.collectAsState()
    val blisterWidth = getKoin().get<Settings>().blisterWidth.dp
    val settings = getKoin().get<Settings>()
    val toast = viewModel.toast.collectAsState()
    val banner = viewModel.banner.collectAsState()

    val infiniteTransition = rememberInfiniteTransition()
    val alpha by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = 0.2f,
        animationSpec = infiniteRepeatable(
            animation = tween(500, easing = LinearEasing),  // Increased duration from 500 to 800
            repeatMode = RepeatMode.Reverse
        )
    )
    LaunchedEffect(Unit) {

        while (true) {
            viewModel.cameraViewModel.value.update()
            viewModel.updateToast()
            delay(100)
        }
    }

    RootSplitViewComponent( footer = {
            when (status.value) {
                BlisterJobViewModelStatus.Active -> {
                    when (medicationStatus.value) {
                        MedicationStatus.Pending -> Components.secondaryButton(
                            "Abbrechen",
                            { cancel(viewModel) })

                        MedicationStatus.InProgress -> {
                            Components.secondaryButton(
                                "Abbrechen",
                                { cancel(viewModel) },
                                modifier = Modifier.weight(2f)
                            )

                            Spacer(modifier = Modifier.width(8.dp))

                            Components.primaryButton(
                                "Weiter",
                                viewModel::takePictureOfMedication,
                                modifier = Modifier.weight(3f)
                            )
                        }

                        else -> {
                            Components.secondaryButton(
                                "Abbrechen",
                                { cancel(viewModel) })
                            Components.primaryButton("Weiter", viewModel::finishMedication)
                        }
                    }
                }

                BlisterJobViewModelStatus.TakeMedicationPicture,
                BlisterJobViewModelStatus.TakeJobPicture -> {
                    cameraViewFooterElements(viewModel)
                }

                BlisterJobViewModelStatus.Finished -> {
                    Components.primaryButton("Zurück", onClick = viewModel::close)
                }

                else -> {
                    Components.primaryButton("Abbrechen", onClick = viewModel::close)
                }
            }
    }, content = {
        Spacer(modifier = Modifier.width(16.dp))
        Column(modifier = Modifier.weight(1f)) {
            Spacer(modifier = Modifier.height(16.dp))
            JobHeaderView(viewModel.job)
            Spacer(modifier = theme.vertical_spacing_xl)

            Box(modifier = Modifier.weight(1f)) {  // Changed to Box with weight

                Column(modifier = Modifier.fillMaxSize())
                {
                    if (medication.value == null) {
                        Box(modifier = Modifier.weight(1f)) {  // Give remaining space to medicationView
                            medicationView(viewModel)
                        }
                        Spacer(modifier = theme.vertical_spacing_xl)
                        if (status.value == BlisterJobViewModelStatus.TakeJobPicture) {
                            cameraView(viewModel)
                        }
                    } else {
                        MedicationInfoView(viewModel)

                        Spacer(modifier = theme.vertical_spacing_xl)

                        Column(modifier = Modifier.weight(1f).fillMaxWidth()) {
                            if (status.value == BlisterJobViewModelStatus.TakeMedicationPicture) {
                                cameraView(viewModel)
                            } else {
                                MedicationDosageView(viewModel)
                            }
                        }
                    }
                }
            }

            Column(modifier = Modifier.fillMaxWidth()) {
                Spacer(modifier = theme.vertical_spacing_xl)
                JobInfoView(viewModel.job, viewModel.blisterConfig)
            }

            Spacer(modifier = Modifier.height(8.dp))
        }
    }, blisterContent = {
            if( toast.value != null ) {
                toast.value?.let { currentToast ->
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color.Black)
                            .padding(8.dp)
                    ) {
                        Text(
                            text = currentToast.message,
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(
                                    when (currentToast.status) {
                                        ToastStatus.Success -> Color.Green.copy(alpha = 0.9f)
                                        ToastStatus.Error -> MaterialTheme.colorScheme.error.copy(alpha = 0.9f)
                                        ToastStatus.Info -> MaterialTheme.colorScheme.secondary.copy(alpha = 0.9f)
                                        else -> MaterialTheme.colorScheme.background
                                    },
                                    shape = MaterialTheme.shapes.medium
                                )
                                .padding(16.dp)
                                .graphicsLayer(alpha = alpha),
                            style = MaterialTheme.typography.titleLarge,
                            color = when (currentToast.status) {
                                ToastStatus.Success -> MaterialTheme.colorScheme.onTertiary
                                ToastStatus.Error -> MaterialTheme.colorScheme.onError
                                ToastStatus.Info -> MaterialTheme.colorScheme.onSecondary
                                else -> MaterialTheme.colorScheme.onBackground
                            },
                            textAlign = TextAlign.Center
                        )
                    }
                }
            } else if( banner.value != null && medicationStatus.value == MedicationStatus.InProgress ) {
                banner.value?.let { bannerText ->
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color.Black)
                            .padding(8.dp)
                    ) {
                        Text(
                            text = bannerText,
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(
                                    Color.Magenta.copy(alpha = 0.9f),
                                    shape = MaterialTheme.shapes.medium
                                )
                                .padding(16.dp)
                                .graphicsLayer(alpha = alpha),
                            style = MaterialTheme.typography.titleLarge,
                            color = MaterialTheme.colorScheme.onError,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }

            when (medicationStatus.value) {
                MedicationStatus.InProgress -> {
                    Spacer(modifier = Modifier.weight(1.0f) )
                    MedicationGridView(viewModel)
                }

                else -> {
                    if( status.value != BlisterJobViewModelStatus.TakeJobPicture ) {
                        Column(modifier = Modifier.fillMaxSize()) {
                            Text(
                                "Bitte wähle ein Medikament aus oder scanne einen Code",
                                modifier = Modifier.padding(8.dp).align(Alignment.CenterHorizontally),
                                style = MaterialTheme.typography.headlineLarge,
                                color = MaterialTheme.colorScheme.tertiary
                            )
                        }
                    }
                }
            }
        })
}

fun cancel( viewModel: BlisterJobViewModel) {

    viewModel.appViewModel.showDialog {
        Dialog("Blistern unterbrechen",
            {
                if( viewModel.medicationStatus.value == MedicationStatus.InProgress) {
                    SelectionView( listOf("Medikament fehlt", "Nicht genügend Tabletten", "Medikament nicht i/o", "Medikament abgelaufen", "Sonstiges"),
                    onSelectionChanged = {

                        viewModel.cancelMedication(it)
                        viewModel.appViewModel.dismissDialog()
                    })
                } else {
                        SelectionView( listOf("Medikamente fehlen","Medikamente nicht i/o","Sonstiges"), onSelectionChanged = {

                            viewModel.cancelMedication(it)
                            viewModel.appViewModel.dismissDialog()
                            viewModel.close()
                        })
                }
            },
            onCloseMessage = "Zurück",
            onClose = {
                viewModel.appViewModel.dismissDialog()
            })
    }
}
