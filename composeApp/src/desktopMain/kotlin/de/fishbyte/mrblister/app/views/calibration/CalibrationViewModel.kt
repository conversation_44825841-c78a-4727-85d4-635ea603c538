package de.fishbyte.mrblister.app.views.calibration

import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.lifecycle.ViewModel
import de.fishbyte.mrblister.app.AppViewModel
import de.fishbyte.mrblister.model.BlisterConfiguration
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.MutableStateFlow

class CalibrationViewModel( val appViewModel: AppViewModel ) : ViewModel() {
    private val _totalWidth = MutableStateFlow(0.dp)
    val totalWidth: StateFlow<Dp> get() = _totalWidth.asStateFlow()

    private val _totalHeight = MutableStateFlow(0.dp)
    val totalHeight: StateFlow<Dp> get() = _totalHeight.asStateFlow()

    private val _rows = MutableStateFlow(5)
    val rows: StateFlow<Int> get() = _rows.asStateFlow()

    private val _columns = MutableStateFlow(7)
    val columns: StateFlow<Int> get() = _columns.asStateFlow()

    private val _rowHeights = MutableStateFlow(List(_rows.value.toInt()) { 0.dp })
    val rowHeights: StateFlow<List<Dp>> get() = _rowHeights.asStateFlow()

    private val _columnWidths = MutableStateFlow(List(_columns.value.toInt()) { 0.dp })
    val columnWidths: StateFlow<List<Dp>> get() = _columnWidths.asStateFlow()

    private val _marginX = MutableStateFlow(8.dp)
    val marginX: StateFlow<Dp> get() = _marginX.asStateFlow()

    private val _marginY = MutableStateFlow(8.dp)
    val marginY: StateFlow<Dp> get() = _marginY.asStateFlow()

    private val _selectedRow = MutableStateFlow(0)
    val selectedRow: StateFlow<Int> get() = _selectedRow.asStateFlow()

    private val _selectedColumn = MutableStateFlow(0)
    val selectedColumn: StateFlow<Int> get() = _selectedColumn.asStateFlow()

    private val _selectionsX = mutableListOf<Dp>()
    private val _selectionsY = mutableListOf<Dp>()

    private val _configs : MutableMap<String,BlisterConfiguration> = mutableMapOf()
    val configs: Map<String,BlisterConfiguration> get() = _configs.toMap()

    private val _selectedConfig = MutableStateFlow<BlisterConfiguration?>(null)
    val selectedConfig: StateFlow<BlisterConfiguration?> get() = _selectedConfig.asStateFlow()

    init {
        appViewModel.blisterConfiguration.forEach {
            _configs[it.name] = it
        }

        if(_configs.isNotEmpty()) {
            selectConfigImpl( _configs.values.first() )
        }
    }

    private fun selectConfigImpl( config : BlisterConfiguration )
    {
        _selectedConfig.value = config
        _selectedRow.value = 0
        _selectedColumn.value = 0
        _selectionsX.clear()
        _selectionsY.clear()
        _rows.value = config.rowHeight.size
        _columns.value = config.columnWidth.size
        _rowHeights.value = config.rowHeight.map { it.dp }
        _columnWidths.value = config.columnWidth.map { it.dp }
        _marginX.value = config.marginX.dp
        _marginY.value = config.marginY.dp
    }

    private fun updateSelectedConfig()
    {
        val previous = _selectedConfig.value
        val updated = BlisterConfiguration(
            name = previous?.name ?: "WeekA4",
            columnWidth = columnWidths.value.map { it.value },
            rowHeight = rowHeights.value.map { it.value },
            marginX = marginX.value.value,
            marginY = marginY.value.value,
            flipTime = previous?.flipTime ?: false,
            flipDays = previous?.flipDays ?: false,
            stations = previous?.stations ?: emptyList()
        )

        _configs[updated.name] = updated
    }

    fun save() {
        updateSelectedConfig()
        appViewModel.saveBlisterConfigurations(_configs.values.toList())
    }

    fun nextSelection( x: Dp, y: Dp)  {

        if( _selectedRow.value == 0 && _selectedColumn.value == 0 ) {
            _selectionsX.clear()
            _selectionsY.clear()
        }

        if( _selectedColumn.value == 0 ) {
            _selectionsY.add(y)
        }

        if( _selectedRow.value == 0 ) {
            _selectionsX.add(x)
        }

        if( _selectedRow.value == _rows.value) {
            _selectedRow.value = 0
            calibrate()
        } else if( _selectedRow.value > 0 || _selectedColumn.value == _columns.value ) {
            _selectedRow.value += 1
            _selectedColumn.value = 0
        } else {
            _selectedColumn.value += 1
        }
    }

    fun resetSelection() {
        _selectedRow.value = 0
        _selectedColumn.value = 0
    }

    private fun calibrate() {
        val x = _selectionsX
        val y = _selectionsY

        /*
        println("${_totalWidth.value} ${_totalHeight.value}")

        println("${x.joinToString(",") }")
        println("${y.joinToString(",") }")
        */

        _marginX.value = _totalWidth.value - x.last()
        _marginY.value = _totalHeight.value - y.last()

        val columns = mutableListOf<Dp>()
        for( column in 0 until _columns.value ) {
            columns.add( x[column+1].minus(x[column]) )
        }
        _columnWidths.value = columns

        val rowScope = mutableListOf<Dp>()
        for( row in 0 until _rows.value ) {
            rowScope.add( y[row+1].minus(y[row]) )
        }
        _rowHeights.value = rowScope
    }

    fun updateMarginX(value: Dp) {
        _marginX.value = value
    }

    fun updateMarginY(value: Dp) {
        _marginY.value = value
    }

    fun updateTotalWidth(value: Dp) {
        _totalWidth.value = value
    }

    fun updateTotalHeight(value: Dp) {
        _totalHeight.value = value
    }

    fun present(appViewModel: AppViewModel) {
        appViewModel.navigation.push(this)
    }

    fun setRows(value: Int) {
        _rows.value = value
        _rowHeights.value = List(value) { 0.dp }
        _selectedRow.value = 0
        _selectedColumn.value = 0
    }

    fun setColumns(value: Int) {
        _columns.value = value
        _columnWidths.value = List(value) { 0.dp }
        _selectedRow.value = 0
        _selectedColumn.value = 0
    }

    fun setRowHeight(dp: Dp) {
        _rowHeights.value = List(_rows.value.toInt()) { 0.dp }
    }

    fun setColumnWidth(dp: Dp) {
        _columnWidths.value = List(_columns.value.toInt()) { 0.dp }
    }

    fun setRowHeight(row: Int, height: Dp) {
        _rowHeights.value = _rowHeights.value.mapIndexed { index, dp ->
            if (index == row) height else dp
        }
    }

    fun setColumnWidth(column: Int, width: Dp) {
        _columnWidths.value = _columnWidths.value.mapIndexed { index, dp ->
            if (index == column) width else dp
        }
    }

    fun selectBlisterConfig(blister: BlisterConfiguration) {
        updateSelectedConfig()
        selectConfigImpl( blister )
    }
}