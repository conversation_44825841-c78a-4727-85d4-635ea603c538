package de.fishbyte.mrblister.app.theme

import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

class DefaultAppTheme : AppTheme {
    override val p_xs: Modifier
        get() = Modifier.padding(1.dp)
    override val p_s: Modifier
        get() = Modifier.padding(2.dp)
    override val p_m: Modifier
        get() = Modifier.padding(4.dp)
    override val p_l: Modifier
        get() = Modifier.padding(6.dp)
    override val p_xl: Modifier
        get() = Modifier.padding(12.dp)
    override val p_xxl: Modifier
        get() = Modifier.padding(24.dp)
    override val m_xs: Dp
        get() = 1.dp
    override val m_s: Dp
        get() = 2.dp
    override val m_m: Dp
        get() = 4.dp
    override val m_l: Dp
        get() = 6.dp
    override val m_xl: Dp
        get() = 12.dp
    override val m_xxl: Dp
        get() = 24.dp
    override val vertical_spacing_xs: Modifier
        get() = Modifier.height(1.dp)
    override val vertical_spacing_s: Modifier
        get() = Modifier.height(2.dp)
    override val vertical_spacing_m: Modifier
        get() = Modifier.height(4.dp)
    override val vertical_spacing_l: Modifier
        get() = Modifier.height(6.dp)
    override val vertical_spacing_xl: Modifier
        get() = Modifier.height(18.dp)
    override val vertical_spacing_xxl: Modifier
        get() = Modifier.height(24.dp)
    override val width_xs: Dp
        get() = 32.dp
    override val width_s: Dp
        get() = 48.dp
    override val width_m: Dp
        get() = 96.dp
    override val width_l: Dp
        get() = 128.dp
    override val width_xl: Dp
        get() = 256.dp
    override val width_xxl: Dp
        get() = 512.dp
    override val height_xs: Dp
        get() = 32.dp
    override val height_s: Dp
        get() = 48.dp
    override val height_m: Dp
        get() = 96.dp
    override val height_l: Dp
        get() = 128.dp
    override val height_xl: Dp
        get() = 256.dp
    override val height_xxl: Dp
        get() = 512.dp

    override val icon_size_xs: Modifier
        get() = Modifier.size(16.dp )
    override val icon_size_s: Modifier
        get() = Modifier.size(32.dp )
    override val icon_size_m: Modifier
        get() = Modifier.size(48.dp )
}