package de.fishbyte.mrblister.app.views.tools

import de.fishbyte.mrblister.model.BlisterPatient
import de.fishbyte.mrblister.model.Patient
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter

fun join( s1: String?, s2: String? ) : String {
    if( s1.isNullOrBlank() ) {
        if( s2.isNullOrBlank() ) {
            return ""
        }
        return s2
    }
    if( s2.isNullOrBlank() ) {
        return s1
    }

    return "$s1 • $s2"
}

fun join( s1: String?, s2: String?, s3: String? ) : String {
    if( s1.isNullOrBlank() ) {
        if( s2.isNullOrBlank() ) {
            if( s3.isNullOrBlank() ) {
                return ""
            }
            return s3
        }
        if( s3.isNullOrBlank() ) {
            return s2
        }
        return "$s2 • $s3"
    }
    if( s2.isNullOrBlank() ) {
        if( s3.isNullOrBlank() ) {
            return s1
        }
        return "$s1 • $s3"
    }
    if( s3.isNullOrBlank() ) {
        return "$s1 • $s2"
    }

    return "$s1 • $s2 • $s3"
}

fun formatShortDate(date: LocalDate): String {
    return date.format(DateTimeFormatter.ofPattern("dd.MM.yyyy"))
}

fun formatShortTimeSpan(start: LocalDate?, end: LocalDate?): String {
    if( start == null ) {
        if( end == null ) {
            return ""
        }
        return formatShortDate(end)
    } else if( end == null ) {
        return formatShortDate(start)
    }
    else {
        return "${start.format(DateTimeFormatter.ofPattern("dd.MM"))} - ${formatShortDate(end)}"
    }
}

fun formatShortDateTime(date: LocalDateTime): String {
    return date.format(DateTimeFormatter.ofPattern("dd.MM.yyyy HH:mm"))
}

fun name( name1: String?, name2: String? ): String {
    if (name1.isNullOrBlank()) {
        if (name2.isNullOrBlank()) {
            return ""
        }
        return name2
    }
    if (name2.isNullOrBlank()) {
        return name1
    }
    return "$name1 $name2"
}

fun name( patient: BlisterPatient? ): String {
    if( patient == null ) {
        return ""
    }
    return name( patient.name1, patient.name2 )
}