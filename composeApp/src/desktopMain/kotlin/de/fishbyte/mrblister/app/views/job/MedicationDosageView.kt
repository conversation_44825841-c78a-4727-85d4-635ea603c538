package de.fishbyte.mrblister.app.views.job

import MedicationDosageModel
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.material.Text
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.theme.AppTheme
import org.koin.mp.KoinPlatform.getKoin
import java.time.DayOfWeek
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import kotlin.streams.toList

@Composable
fun MedicationDosageView(viewModel: BlisterJobViewModel) {

    val theme = getKoin().get<AppTheme>()

    val timeFormatter = DateTimeFormatter.ofPattern("HH:mm")

    val medication = viewModel.medication.collectAsState()
    val isFirstColor = viewModel.firstColor.collectAsState()

    val medicationDosages = MedicationDosageModel.mergeDays( medication.value?.medications?.map { MedicationDosageModel.fromMedication(it) }?.toList() ?: emptyList() )

    // Prepare column headers
    val columnHeaders = viewModel.rowsUnsorted.stream().filter{ it -> it != null}.map{ it?.format(timeFormatter) }.toList() + listOf("Tag(e)")

    val columnCount = columnHeaders.size -1

    val modelRows = viewModel.rowsUnsorted.stream().filter{ it -> it != null}.toList()

    ElevatedCard(
        elevation = CardDefaults.cardElevation(defaultElevation = 6.dp),
        modifier = Modifier.fillMaxSize()) {
        Column(modifier = Modifier.padding(16.dp)) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                for (header in columnHeaders) {
                    val weight = if( columnHeaders.last() == header ) 2.0f else 1.0f
                    Text(
                        text = header ?: "",
                        textAlign = TextAlign.Center,
                        fontSize = MaterialTheme.typography.titleLarge.fontSize,
                        modifier = Modifier.background(Color.Gray).weight(weight)
                            .border(width = 1.dp, color = Color.Black).padding(4.dp)
                    )
                }
            }

            Box {
                val listState = rememberLazyListState()

                LazyColumn(state = listState) {
                    items(medicationDosages.size) { index ->
                        val medicationDosage = medicationDosages[index]

                        Row(
                            modifier = Modifier.border(width = 1.dp, color = Color.Black),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            for (i in 0..<columnCount) {
                                val dosage = medicationDosage.dosages[modelRows.get(i) ?: LocalTime.of(23, 59)]

                                val color = if (dosage == null) Color.Transparent else getColorForDosage(
                                    dosage,
                                    isFirstColor.value
                                )
                                val text = if (dosage == null) "" else formatDosage(dosage)
                                Box(
                                    modifier = Modifier.weight(1.0f).fillMaxSize().background(Color.Gray)
                                        .border(width = 1.dp, color = Color.Black).background(color).padding(4.dp)
                                ) {
                                    Text(
                                        text, modifier = Modifier.fillMaxSize(), textAlign = TextAlign.Center,
                                        fontSize = MaterialTheme.typography.titleLarge.fontSize,
                                        color = Color.White
                                    )
                                }
                            }
                            Text(
                                text = medicationDosage.getLocalizedWeekDays(),
                                textAlign = TextAlign.Center,
                                color = getColorForWeekDays(medicationDosage,isFirstColor.value),
                                fontSize = MaterialTheme.typography.titleLarge.fontSize,
                                modifier = Modifier.background(Color.Gray).weight(2.0f).fillMaxSize()
                                    .border(width = 1.dp, color = Color.Black).padding(4.dp)
                            )
                        }
                    }
                }

                VerticalScrollbar(
                    modifier = Modifier.align(Alignment.CenterEnd) /*.fillMaxHeight()*/,
                    adapter = rememberScrollbarAdapter(listState)
                )
            }
        }
    }
}

fun getColorForWeekDays(medicationDosage: MedicationDosageModel, firstColor: Boolean): Color {
    if( !firstColor ) {
        if( !medicationDosage.isAllWeek() ) {
            return Color.Red
        }
    }
    return Color.Black
}
