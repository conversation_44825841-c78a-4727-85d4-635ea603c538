package de.fishbyte.mrblister.app

import com.github.eduramiba.webcamcapture.drivers.NativeDriver

import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.type
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.window.Window
import androidx.compose.ui.window.WindowPosition
import androidx.compose.ui.window.WindowState
import androidx.compose.ui.window.application
import com.fazecast.jSerialComm.SerialPort
import com.github.sarxos.webcam.Webcam
import de.fishbyte.mrblister.app.views.startup.loadConfiguration
import org.koin.core.context.GlobalContext.startKoin
import org.koin.mp.KoinPlatform.getKoin
import java.awt.GraphicsDevice
import java.awt.GraphicsEnvironment
import java.awt.Rectangle
import java.io.File

fun openSerialPort(portName: String, baudRate: Int = 9600): SerialPort? {
    val port = SerialPort.getCommPort(portName)
    port.setBaudRate(baudRate)
    port.setNumDataBits(8)
    port.setNumStopBits(1)
    port.setParity(SerialPort.NO_PARITY)
    port.setFlowControl(SerialPort.FLOW_CONTROL_XONXOFF_IN_ENABLED or SerialPort.FLOW_CONTROL_XONXOFF_OUT_ENABLED)

    return if (port.openPort()) port else null
}

fun setupConfigPath( configPath: String? ) : String?
{
    if( configPath != null ) {
        return configPath
    }
    if (System.getProperty("os.name").startsWith("Windows")) {
        val path = System.getenv("LOCALAPPDATA") + "\\MrBlister"

        if (!java.io.File(path).exists()) {
            java.io.File(path).mkdirs()
        }

        return path
    }
    if( System.getProperty("os.name").startsWith("Mac") ) {
        val path = System.getProperty("user.home") + "/Library/Application Support/MrBlister"
        if (!java.io.File(path).exists()) {
            java.io.File(path).mkdirs()
        }
        return path
    }

    return null
}

fun createDefaultSettings(configPath: String) {
    val settingsFile = File("$configPath/settings.xml")
    if (!settingsFile.exists()) {
        settingsFile.writeText("""
<?xml version="1.0" encoding="UTF-8"?>
<Settings>
    <JobsPath>samples</JobsPath>
    <UserFilePath>users.json</UserFilePath>
    <StoragePath>storage</StoragePath>
    <Picture>ALL</Picture>
    <MultiMonitor>false</MultiMonitor>
    <BlisterWidth>1600</BlisterWidth>
    <CameraCountDown>6</CameraCountDown>
    <PatientScanNag>false</PatientScanNag>
    <MedicationScanNag>false</MedicationScanNag>
    <PrimaryMonitor>Display 2</PrimaryMonitor>
    <SecondaryMonitor>Display 1</SecondaryMonitor>
</Settings>
        """.trimIndent())
    }
}

fun createDefaultUser(configPath: String) {
    val userFile = File("$configPath/users.json")
    if (!userFile.exists()) {
        userFile.writeText("""
            {
                "users": [
                    {
                        "name": "Christian",
                        "pinHash": "${'$'}2a${'$'}12${'$'}d1UWLrMg2D8j7TIR7KWspe47PpvB1KIGDK6seGxA/pzDbw4o5O.5i",
                        "role": "ADMIN"
                    }
                ]
            }
        """.trimIndent())
    }
}

fun main(args: Array<String>) = application {

    val configPath = setupConfigPath(args.getOrNull(0))
    if( configPath == null ) {
       println("No config path provided")
       exitApplication()
       return@application
    }

    createDefaultUser(configPath)
    createDefaultSettings(configPath)

    startKoin {
        modules(appModule(configPath))
    }

    val settings = getKoin().get<Settings>()
    val viewModel = getKoin().get<AppViewModel>()

    loadConfiguration(configPath, settings)

    if( System.getProperty("os.name").startsWith("Mac") ) {
        Webcam.setDriver(NativeDriver())
    } else {
       Webcam.setDriver(com.github.eduramiba.webcamcapture.drivers.NativeDriver())
   }

    val screens = GraphicsEnvironment.getLocalGraphicsEnvironment().screenDevices

    println("Screens: ${screens.joinToString(",") { it.iDstring }}")

    val primaryScreen = findScreen( settings.primaryMonitor, screens.toList(), screens[0] )

    /*
    val secondaryScreen = if( settings.multiMonitor ) {
        if( settings.secondaryMonitor.isNullOrBlank() ) {
            screens[1]
        } else {
            screens.first { it.iDstring == settings.secondaryMonitor }
        }
    } else {
        null
    }

     */

    viewModel.primaryScreen = primaryScreen
   // viewModel.secondaryScreen = secondaryScreen

    // Get the bounds of the first and second monitors
    val primaryScreenBounds: Rectangle = primaryScreen.defaultConfiguration.bounds

    val barcode = StringBuilder()

    // Create a window for the primary screen
    Window(
        onCloseRequest = ::exitApplication,
        title = "Primary Screen Window",
        state = WindowState(
            position = WindowPosition(
                Dp(
                    primaryScreenBounds.x.toFloat()
                ),
                Dp(primaryScreenBounds.y.toFloat())
            ),
            size = DpSize(
                Dp(primaryScreenBounds.width.toFloat()),
                Dp(primaryScreenBounds.height.toFloat())
            ),
            placement = androidx.compose.ui.window.WindowPlacement.Fullscreen
        ),
        onKeyEvent = {
            if( it.type == KeyEventType.KeyUp ) {

                when (it.key.keyCode) {
                    Key.Enter.keyCode -> {
                        if( settings.scannerPort == null ) {
                            if (barcode.isNotEmpty()) {
                                println("barcode: ${barcode.toString()}")
                                viewModel.handleBarcode(barcode.toString())
                            }
                            barcode.clear()
                        }
                    }
                    Key.Spacebar.keyCode, Key.Escape.keyCode -> {
                        viewModel.handleActionKey(it.key.keyCode)
                    }

                    in Key.Zero.keyCode..Key.Nine.keyCode -> {
                        if( settings.scannerPort == null ) {
                            barcode.append(it.key.keyCode.toInt().toChar())
                        }
                    }
                    else -> {
                        barcode.clear()
                    }
                }
            }
            true
        }
    ) {
        // Composable content for the primary screen window
        App(configPath)
    }
/*
        val secondaryScreenBounds: Rectangle = screens[0].defaultConfiguration.bounds

        // Create a window for the secondary screen
        Window(
            onCloseRequest = ::exitApplication,
            title = "Secondary Screen Window",
            state = WindowState(
                position = WindowPosition(
                    Dp(
                        secondaryScreenBounds.x.toFloat()
                    ),
                    Dp(secondaryScreenBounds.y.toFloat())
                ),
                size = DpSize(
                    Dp(secondaryScreenBounds.width.toFloat()),
                    Dp(secondaryScreenBounds.height.toFloat())
                )
            ),
            undecorated = true,
            resizable = false,
            focusable = false,
            alwaysOnTop = true
        ) {
            // Composable content for the secondary screen window
        }

 */

}

fun findScreen( idString: String?, screens: List<GraphicsDevice>, default: GraphicsDevice ): GraphicsDevice {
    if( !idString.isNullOrBlank() ) for (c in screens) {
        if (c.iDstring == idString) {
            return c
        }
    }

    return default
}