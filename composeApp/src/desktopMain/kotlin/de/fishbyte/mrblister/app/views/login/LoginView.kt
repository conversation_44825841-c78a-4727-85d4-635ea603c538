package de.fishbyte.mrblister.app.views.login
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.AppViewModel
import de.fishbyte.mrblister.app.Settings
import org.koin.compose.getKoin
// Add this import
import androidx.compose.material.DropdownMenu
import androidx.compose.material.DropdownMenuItem
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.input.key.*
import de.fishbyte.mrblister.app.views.AppVersionInfo
import de.fishbyte.mrblister.components.RootSplitViewComponent
import de.fishbyte.mrblister.util.VersionInfo
import kotlin.math.exp
import kotlin.system.exitProcess

@Composable
fun LoginView()
{
    val focusRequester = remember { FocusRequester() }
    val passwordFocusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current

    // Add this effect after the state declarations
    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }

    val appViewModel = getKoin().get<AppViewModel>()
    val viewModel = remember { LoginViewModel(appViewModel) }
    val settings = getKoin().get<Settings>()

    val currentUserName = viewModel.userName.collectAsState()

    val validUser = viewModel.validUser.collectAsState()
    val password = viewModel.password.collectAsState()
    val errorMessage = viewModel.errorMessage.collectAsState()

    RootSplitViewComponent( content = {

        Column(
            modifier = Modifier
                .weight(1f)
                .fillMaxHeight()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "MrBlister Anmeldung",
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.padding(bottom = 32.dp)
            )

            // Add error message display
            errorMessage.value?.let { error ->
                Text(
                    text = error,
                    color = MaterialTheme.colorScheme.error,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
            }

            Box(
                modifier = Modifier
                    .width(320.dp)
                    .padding(bottom = 24.dp)
            ) {
                var expanded by remember { mutableStateOf(false) }
                val users = appViewModel.userRepository.getUsers()

                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { expanded = true }
                ) {
                    OutlinedTextField(
                        value = currentUserName.value ?: "",
                        onValueChange = viewModel::updateUserName,
                        label = { Text("Benutzer") },
                        suffix = {
                            if (validUser.value) {
                                Icon(
                                    imageVector = Icons.Default.CheckCircle,
                                    contentDescription = "Valid user",
                                    tint = Color.Green,
                                    modifier = Modifier.padding(start = 8.dp)
                                )
                            }
                        },
                        singleLine = true,
                        modifier = Modifier
                            .fillMaxWidth()
                            .focusRequester(focusRequester)
                            .onFocusChanged { if (it.isFocused) expanded = true },
                        readOnly = true
                    )
                }

                DropdownMenu(
                    expanded = expanded,
                    onDismissRequest = { expanded = false },
                    modifier = Modifier.width(320.dp)
                ) {
                    users.forEach { user ->
                        DropdownMenuItem(
                            onClick = {
                                viewModel.updateUserName(user.name)
                                viewModel.updatePassword("")
                                expanded = false
                                passwordFocusRequester.requestFocus()
                            }
                        ) {
                            Text(user.name)
                        }
                    }
                }
            }
            OutlinedTextField(
                value = password.value ?: "",
                onValueChange = viewModel::updatePassword,
                label = { Text("PIN/Passwort") },
                singleLine = true,
                visualTransformation = PasswordVisualTransformation(),
                modifier = Modifier
                    .width(320.dp)
                    .padding(bottom = 24.dp)
                    .focusRequester(passwordFocusRequester)
                    .onKeyEvent {
                        if (it.key == Key.Enter) {
                            viewModel.performLogin()
                            return@onKeyEvent true
                        }
                        false
                    }
            )
            Button(
                onClick = { viewModel.performLogin() },
                modifier = Modifier.width(280.dp),
                enabled = !currentUserName.value.isNullOrEmpty() && !password.value.isNullOrEmpty(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    contentColor = MaterialTheme.colorScheme.onPrimary
                )
            ) {
                Text("Anmeldung")
            }
            OutlinedButton(
                onClick = { exitProcess(0) },
                modifier = Modifier.width(280.dp),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Text("Beenden")
            }

            Spacer(modifier = Modifier.height(16.dp))
            AppVersionInfo()
        }
    }, blisterContent = {
            // TODO
    })
}
