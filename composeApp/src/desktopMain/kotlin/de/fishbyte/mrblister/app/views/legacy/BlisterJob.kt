package de.fishbyte.mrblister.app.views.legacy

/*
@Composable
fun blisterJob(blisterJob: BlisterJob, goBack: () -> Unit, confirm: () -> Unit) {

    val viewModel = getKoin().get<BlisterViewModel>();
    val patient = blisterJob.patient
    val job = blisterJob.job

    RootViewComponent(footer = {
        Components.secondaryButton("Zurück", goBack)
        Spacer(modifier = Modifier.weight(1f))
        Components.primaryButton("Behandlung bestätigen", confirm) }) {

        Row {

            val days = listOf("Mo", "Di", "Mi", "Do", "Fr", "Sa", "So")
            val rows = listOf("Sober", "Morning", "Noon", "Evening", "Night")

            val grid = createGridData(rows.size, days.size)

            val firstDate = job.medications.medicationList.minByOrNull { it.takingDate }?.takingDate
            val nearestMonday = firstDate?.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))

            job.medications.medicationList.forEach { medication ->

                val rowIdx = rows.indexOf(medication.takingRow ?: medication.takingColumn)
                if (rowIdx < 0 || rowIdx > rows.size) {
                    throw IllegalStateException("takingRow/takingColumn not matching the blister")
                }
                val columnIdx =
                    nearestMonday?.let { date -> ChronoUnit.DAYS.between(date, medication.takingDate).toInt() }
                        ?: -1

                if (columnIdx < 0 && columnIdx >= days.size) {
                    throw IllegalStateException("takingDate not matching the blister")
                }

                grid[rowIdx][columnIdx].add(medication)

            }

            Column(modifier = Modifier.width(480.dp).fillMaxHeight()) {
                patient?.title?.let {
                    Text(it, style = MaterialTheme.typography.h4)
                }
                Text( patient?.name1 + " " + patient?.name2, style = MaterialTheme.typography.h3 )
                patient?.nursingHome?.let { nursingHome ->
                    Box {
                        Column {
                            nursingHome.name?.let { Text(it, style = MaterialTheme.typography.subtitle1) }
                            nursingHome.station?.let {
                                Text(
                                    "Station: $it",
                                    style = MaterialTheme.typography.subtitle2
                                )
                            }
                            nursingHome.room?.let { Text("Room: $it", style = MaterialTheme.typography.subtitle2) }
                        }
                    }
                }
            }


            GridWithHeaders(
                columnHeaders = days,
                rowHeaders = rows,
                gridContent = grid,
                modifier = Modifier.weight(1f),
                item = { medications ->
                    Text(medications.stream().map {
                        val drug = blisterJob.drugById(it.drugID)
                            ?: throw IllegalStateException("Drug ${it.drugID} not listed!")

                        "${it.dosage} ${drug.name}"
                    }.collect(Collectors.joining("\n")))
                }
            )

        }
    }
}

fun createGridData(rows: Int, columns: Int): MutableList<MutableList<MutableList<Medication>>> {
    // Create the outer mutable list (for rows)
    val gridData = MutableList(rows) {
        // For each row, create a mutable list of columns
        MutableList(columns) {
            // For each column, create a mutable list for the cell data (initially empty)
            mutableListOf<Medication>()
        }
    }
    return gridData
}


@Composable
fun GridItem(item: String) {
    BasicText(
        text = item,
        style = MaterialTheme.typography.body2,
        modifier = Modifier.border(1.dp, Color.LightGray).padding(16.dp)
    )
}

 */