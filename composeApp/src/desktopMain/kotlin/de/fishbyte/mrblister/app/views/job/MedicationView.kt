package de.fishbyte.mrblister.app.views.job

import androidx.compose.animation.core.*
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import compose.icons.FontAwesomeIcons
import compose.icons.fontawesomeicons.Regular
import compose.icons.fontawesomeicons.regular.CheckCircle
import compose.icons.fontawesomeicons.regular.Circle
import de.fishbyte.mrblister.app.theme.AppTheme
import org.koin.compose.getKoin
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import de.fishbyte.mrblister.model.*
import java.util.stream.Collectors

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun medicationView(viewModel: BlisterJobViewModel) {
    val medications = viewModel.medications.collectAsState();
    medicationView2(medications.value,
        { summary -> viewModel.appViewModel.getOrCreateJobStatus(viewModel.job).getMedicationStatus(summary.drug.id) },
        { drug -> viewModel.startMedicationManual(drug)} )
}

@Composable
fun medicationView2(medications: List<MedicationSummary>, statusSupplier: (MedicationSummary) -> MedicationStatusModel?, onClick: (Drug) -> Unit)
{
    val theme = getKoin().get<AppTheme>()
    ElevatedCard(
        elevation = CardDefaults.cardElevation(defaultElevation = 6.dp),
        modifier = Modifier.fillMaxWidth()) {
        Column( Modifier.padding(16.dp)) {
            Row() {
                Spacer(modifier = Modifier.width(32.dp))
                Text(
                    "Medikament",
                    modifier = Modifier.weight(6.0f).padding(8.dp),
                    style = MaterialTheme.typography.labelSmall
                )
                Text(
                    "Darreichungsform",
                    modifier = Modifier.weight(3.0f).padding(8.dp),
                    style = MaterialTheme.typography.labelSmall
                )
                Text(
                    "Menge",
                    modifier = Modifier.weight(1.0f).padding(8.dp),
                    style = MaterialTheme.typography.labelSmall
                )
            }

            val infiniteTransition = rememberInfiniteTransition()
            val alpha by infiniteTransition.animateFloat(
                initialValue = 1f,
                targetValue = 0f,
                animationSpec = infiniteRepeatable(
                    animation = tween(500, easing = LinearEasing),
                    repeatMode = RepeatMode.Reverse
                )
            )

            Box {
                val listState = rememberLazyListState()

                val dataSource = medications
                val padding = PaddingValues(horizontal = 6.dp, vertical = 2.dp)

                LazyColumn(state = listState) {
                    items(dataSource.size) { index ->
                        val summary = dataSource[index]
                        val drug = summary.drug

                        val information = summary.medications.stream().map { it.information }
                            .filter { info -> info?.isNotEmpty() == true }.distinct().collect(
                                Collectors.joining(", ")
                            )

                        Row(
                            modifier = Modifier.background(
                                MaterialTheme.colorScheme.surface,
                                MaterialTheme.shapes.medium
                            ).clickable{ onClick(drug) },
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Column( modifier = Modifier.fillMaxWidth() ) {
                                Row(modifier = Modifier.fillMaxWidth()) {
                                    Icon(
                                        imageVector = when (summary.status) {
                                            MedicationStatus.Pending -> {
                                                FontAwesomeIcons.Regular.Circle
                                            }

                                            MedicationStatus.Completed -> {
                                                FontAwesomeIcons.Regular.CheckCircle
                                            }

                                            else -> {
                                                FontAwesomeIcons.Regular.Circle
                                            }
                                        },
                                        contentDescription = "Medikament",
                                        modifier = theme.icon_size_s.padding(padding)
                                    )
                                    Text(drug.name, modifier = Modifier.weight(6.0f).padding(padding))
                                    if (information?.lowercase()?.contains("blister") == true) {
                                        androidx.compose.material.Text(
                                            information,
                                            modifier = Modifier.weight(3f).padding(padding).graphicsLayer(alpha = alpha),
                                        )
                                    } else {
                                        androidx.compose.material.Text(
                                            "--",
                                            modifier = Modifier.weight(3f).padding(padding)
                                        )
                                    }
                                    Text(formatDosage(summary.dosage), modifier = Modifier.weight(1.0f).padding(padding))
                                }

                                val status = statusSupplier(summary)
                                if (status != null && status.cancelReason.isNotEmpty()) {
                                    Row(
                                        modifier = Modifier.background(
                                            MaterialTheme.colorScheme.surface,
                                            MaterialTheme.shapes.medium
                                        ),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(status.cancelReason, modifier = Modifier.padding(padding))
                                    }
                                }
                            }
                        }
                        Spacer(modifier = theme.vertical_spacing_s)
                    }
                }

                VerticalScrollbar(
                    modifier = Modifier.align(Alignment.CenterEnd).fillMaxHeight(),
                    adapter = rememberScrollbarAdapter(listState)
                )
            }
        }
    }
}




fun getColorForDosage( dosage: Double, isFirstColor: Boolean ) : Color  {
        val baseColors = listOf(
            Color(0xFF000000), // Black for 0
            Color(0xFF43A047), // Green
            Color(0xFFE53935), // Red
            Color(0xFF1E88E5), // Blue
            Color(0xFFFDD835), // Yellow
            Color(0xFFFF6F00), // Orange
            Color(0xFF8E24AA), // Purple
            Color(0xFF00ACC1), // Cyan
            Color(0xFF3949AB), // Indigo
            Color(0xFFFF69B4) // Hot Pink - more distinct final color
    )

    val wholePart = dosage.toInt()
    val hasFraction = dosage % 1 != 0.0

    return when {
        hasFraction -> if (isFirstColor) baseColors[(wholePart + 1).coerceAtMost(baseColors.size - 1)] else Color.Black
        else -> baseColors[wholePart.coerceAtMost(baseColors.size - 1)]
    }
}

/**
 * Formats a decimal number into a mixed number representation using common fractions.
 *
 * The function converts the fractional part of a `Double` into a textual fraction
 * (e.g., `1.5` becomes `"1 1/2"`), supporting only the following fraction approximations:
 * - `0.25` → `"1/4"`
 * - `0.5`  → `"1/2"`
 * - `0.66` → `"2/3"`
 * - `0.75` → `"3/4"`
 *
 * The mapping uses a tolerance (epsilon) of `0.01` to determine if a value is close enough
 * to a known fraction. If no matching fraction is found within the epsilon range,
 * the original decimal value is returned as a string with no special formatting.
 *
 * ### Examples:
 * ```
 * formatFraction(0.0)    // "0"
 * formatFraction(0.5)    // "1/2"
 * formatFraction(1.0)    // "1"
 * formatFraction(1.5)    // "1 1/2"
 * formatFraction(1.66)   // "1 2/3"
 * formatFraction(2.25)   // "2 1/4"
 * formatFraction(3.74)   // "3.74" (not within epsilon of any known fraction)
 * ```
 *
 * @param value The decimal number to format.
 * @return A string representing the number using whole numbers and/or common fractions.
 */
fun formatDosage( dosage: Double ) : String {
    val wholePart = dosage.toInt()
    val fractionalPart = dosage - wholePart

    val fraction = when {
        Math.abs(fractionalPart - 0.25) < 0.02 -> "¼"
        Math.abs(fractionalPart - 0.33) < 0.02 -> "⅓"
        Math.abs(fractionalPart - 0.5) < 0.02 -> "½"
        Math.abs(fractionalPart - 0.66) < 0.02 -> "⅔"
        Math.abs(fractionalPart - 0.75) < 0.02 -> "¾"
        Math.abs(fractionalPart - 0.0) < 0.02 -> "0"
        else -> return dosage.toString()
    }

    return when {
        wholePart == 0 -> fraction
        fraction == "0" -> wholePart.toString()
        else -> "$wholePart $fraction"
    }
}