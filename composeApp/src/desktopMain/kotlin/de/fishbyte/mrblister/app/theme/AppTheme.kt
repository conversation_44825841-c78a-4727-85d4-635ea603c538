package de.fishbyte.mrblister.app.theme

import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp

interface AppTheme {

    val p_xs: Modifier
    val p_s: Modifier
    val p_m: Modifier
    val p_l: Modifier
    val p_xl: Modifier
    val p_xxl: Modifier

    val m_xs: Dp
    val m_s: Dp
    val m_m: Dp
    val m_l: Dp
    val m_xl: Dp
    val m_xxl: Dp

    val vertical_spacing_xs: Modifier
    val vertical_spacing_s: Modifier
    val vertical_spacing_m: Modifier
    val vertical_spacing_l: Modifier
    val vertical_spacing_xl: Modifier
    val vertical_spacing_xxl: Modifier

    val width_xs: Dp
    val width_s: Dp
    val width_m: Dp
    val width_l: Dp
    val width_xl: Dp
    val width_xxl: Dp

    val height_xs: Dp
    val height_s: Dp
    val height_m: Dp
    val height_l: Dp
    val height_xl: Dp
    val height_xxl: Dp

    val icon_size_xs: Modifier
    val icon_size_s: Modifier
    val icon_size_m: Modifier
}