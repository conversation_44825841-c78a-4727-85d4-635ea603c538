package de.fishbyte.mrblister.app.views.legacy

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material3.Button
import androidx.compose.material3.ListItem
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.views.tools.name
import de.fishbyte.mrblister.components.RootViewComponent
import de.fishbyte.mrblister.model.BlisterPatient
import de.fishbyte.mrblister.model.Patient

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun patientsWithJobsList( patientList: List<BlisterPatient>, startBlister: (BlisterPatient) -> Unit ) {

    RootViewComponent( /* footer = { Button({ goBack() }, content = { Text("Zurück") }) }  */ ) {
        Column(modifier = Modifier.padding(20.dp)) {
            Text("Liste der Patienten")
            Spacer(
                modifier = Modifier.height(12.dp)
            )
            patientList.forEach { patient ->
                patientItem(patient, startBlister)
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun patientItem(patient: BlisterPatient, startBlister: (BlisterPatient) -> Unit) {
    ListItem(

        headlineContent = { Text( name(patient) ) },
        supportingContent = { patient.nursingHome?.name?.let { Text(it) } ?: null },
        trailingContent = {
            Button(
                { startBlister(patient) },
                content = {
                    Text("Start befüllen")
                }
            )
        }
    )
}


