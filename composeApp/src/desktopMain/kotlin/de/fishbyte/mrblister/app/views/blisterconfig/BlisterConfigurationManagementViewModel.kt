package de.fishbyte.mrblister.app.views.blisterconfig

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import de.fishbyte.mrblister.app.AppViewModel
import de.fishbyte.mrblister.app.views.calibration.CalibrationViewModel
import de.fishbyte.mrblister.app.views.job.MedicationGridViewModel
import de.fishbyte.mrblister.model.BlisterConfiguration

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime
import java.time.temporal.TemporalAdjusters

enum class BlisterConfigMode {
    LIST,
    DETAILS,
    EDIT,
    CREATE
}

class BlisterConfigurationManagementViewModel(val appViewModel: AppViewModel) : ViewModel() {
    private val _configurations = MutableStateFlow<List<BlisterConfiguration>>(emptyList())
    val configurations: StateFlow<List<BlisterConfiguration>> get() = _configurations.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> get() = _errorMessage.asStateFlow()

    private val _successMessage = MutableStateFlow<String?>(null)
    val successMessage: StateFlow<String?> get() = _successMessage.asStateFlow()

    private val _selectedConfiguration = MutableStateFlow<BlisterConfiguration?>(null)
    val selectedConfiguration: StateFlow<BlisterConfiguration?> get() = _selectedConfiguration.asStateFlow()

    // New configuration form fields
    private val _newConfigName = MutableStateFlow("")
    val newConfigName: StateFlow<String> get() = _newConfigName.asStateFlow()

    private val _newConfigRows = MutableStateFlow(4)
    val newConfigRows: StateFlow<Int> get() = _newConfigRows.asStateFlow()

    private val _newConfigFlipTime = MutableStateFlow(false)
    val newConfigFlipTime: StateFlow<Boolean> get() = _newConfigFlipTime.asStateFlow()

    private val _newConfigFlipDays = MutableStateFlow(false)
    val newConfigFlipDays: StateFlow<Boolean> get() = _newConfigFlipDays.asStateFlow()

    private val _newConfigStations = MutableStateFlow("")
    val newConfigStations: StateFlow<String> get() = _newConfigStations.asStateFlow()

    // Edit configuration form fields
    private val _editConfigName = MutableStateFlow("")
    val editConfigName: StateFlow<String> get() = _editConfigName.asStateFlow()

    private val _editConfigRows = MutableStateFlow(4)
    val editConfigRows: StateFlow<Int> get() = _editConfigRows.asStateFlow()

    private val _editConfigFlipTime = MutableStateFlow(false)
    val editConfigFlipTime: StateFlow<Boolean> get() = _editConfigFlipTime.asStateFlow()

    private val _editConfigFlipDays = MutableStateFlow(false)
    val editConfigFlipDays: StateFlow<Boolean> get() = _editConfigFlipDays.asStateFlow()

    private val _editConfigStations = MutableStateFlow("")
    val editConfigStations: StateFlow<String> get() = _editConfigStations.asStateFlow()

    private val _mode = MutableStateFlow(BlisterConfigMode.LIST)
    val mode: StateFlow<BlisterConfigMode> get() = _mode.asStateFlow()

    private val _gridViewModel = MutableStateFlow<MedicationGridViewModel?>(null)
    val gridViewModel: StateFlow<MedicationGridViewModel?> get() = _gridViewModel.asStateFlow()

    init {
        refreshConfigurations()
    }

    private fun refreshConfigurations() {
        _configurations.value = appViewModel.blisterConfiguration
    }

    fun updateNewConfigName(name: String) {
        _newConfigName.value = name
        _errorMessage.value = null
    }

    fun updateNewConfigRows(rows: Int) {
        _newConfigRows.value = rows.coerceIn(1, 10)
        _errorMessage.value = null
    }

    fun updateNewConfigFlipTime(flipTime: Boolean) {
        _newConfigFlipTime.value = flipTime
        _errorMessage.value = null
        updateGridViewModel()
    }

    fun updateNewConfigFlipDays(flipDays: Boolean) {
        _newConfigFlipDays.value = flipDays
        _errorMessage.value = null
        updateGridViewModel()
    }

    fun updateNewConfigStations(stations: String) {
        _newConfigStations.value = stations
        _errorMessage.value = null
    }

    fun updateEditConfigName(name: String) {
        _editConfigName.value = name
        _errorMessage.value = null
        updateGridViewModel()
    }

    fun updateEditConfigRows(rows: Int) {
        _editConfigRows.value = rows.coerceIn(1, 10)
        _errorMessage.value = null
        updateGridViewModel()
    }

    fun updateEditConfigFlipTime(flipTime: Boolean) {
        _editConfigFlipTime.value = flipTime
        _errorMessage.value = null
        updateGridViewModel()
    }

    fun updateEditConfigFlipDays(flipDays: Boolean) {
        _editConfigFlipDays.value = flipDays
        _errorMessage.value = null
        updateGridViewModel()
    }

    fun updateEditConfigStations(stations: String) {
        _editConfigStations.value = stations
        _errorMessage.value = null
    }

    fun selectConfiguration(config: BlisterConfiguration) {
        _selectedConfiguration.value = config

        _editConfigName.value = config.name
        _editConfigRows.value = config.rowHeight.size
        _editConfigFlipTime.value = config.flipTime ?: false
        _editConfigFlipDays.value = config.flipDays ?: false
        _editConfigStations.value = config.stations.joinToString(", ")
        _errorMessage.value = null

        _mode.value = BlisterConfigMode.DETAILS
        updateGridViewModel()
    }

    private fun updateGridViewModel() {
        var config : BlisterConfiguration? = null

        when(_mode.value) {
            BlisterConfigMode.LIST, BlisterConfigMode.DETAILS -> {
                config = _selectedConfiguration.value
            }
            BlisterConfigMode.EDIT -> {


                _selectedConfiguration.value?.let {
                    val rowHeights = it.rowHeight.take(_editConfigRows.value).toMutableList()
                    if( rowHeights.size < _editConfigRows.value ) {
                        rowHeights.addAll( List(_editConfigRows.value - rowHeights.size) { 190f } )
                    }

                    config = BlisterConfiguration(
                        name = _editConfigName.value,
                        columnWidth = it.columnWidth ?: emptyList(),
                        rowHeight = rowHeights,
                        marginX = it.marginX,
                        marginY = it.marginY,
                        flipTime = _editConfigFlipTime.value,
                        flipDays = _editConfigFlipDays.value,
                        stations = it.stations
                    )
                }
            }
            BlisterConfigMode.CREATE -> {

                // Create default column widths (7 columns)
                val defaultColumnWidths = List(7) { 180f }

                // Create row heights based on number of rows
                val defaultRowHeights = List(newConfigRows.value) { 190f }

                // Create new configuration
                config = BlisterConfiguration(
                    name = newConfigName.value,
                    columnWidth = defaultColumnWidths,
                    rowHeight = defaultRowHeights.take(_newConfigRows.value),
                    marginX = 62f,
                    marginY = 62f,
                    flipTime = _newConfigFlipTime.value,
                    flipDays = _newConfigFlipDays.value,
                    stations = emptyList()
                )
            }
        }

        if( config != null ) {
            _gridViewModel.value = MedicationGridViewModel()

            val start = LocalDate.now().with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
            val end = start.plusDays(6)

            val takingDates = mutableListOf<LocalDate>()
            var currentDate = start
            while (!currentDate.isAfter(end)) {
                takingDates.add(currentDate)
                currentDate = currentDate.plusDays(1)
            }
            _gridViewModel.value?.start(
                takingDates,
                config!!,
                getSampleTimes(config!!)
            )
        } else {
            _gridViewModel.value = null
        }
    }

    private fun getSampleTimes(selectedConfig: BlisterConfiguration): List<Pair<LocalTime,Int>> {
        val allTimes = listOf(
            Pair(LocalTime.of(6, 0),0),
            Pair(LocalTime.of(8, 0),1),
            Pair(LocalTime.of(12, 0),2),
            Pair(LocalTime.of(18, 0),3),
            Pair(LocalTime.of(20, 0),4),
            Pair(LocalTime.of(21, 0),5),
            Pair(LocalTime.of(22, 0),6),
            Pair(LocalTime.of(23, 0),7),
        )
        return allTimes.take(selectedConfig.rowHeight.size)
    }

    fun deleteConfiguration( configToDelete: BlisterConfiguration) {
        deleteConfigurationConfirmDialog(
            appViewModel,
            configuration = configToDelete,
            onDismiss = { confirmed ->
                if (confirmed) {
                    removeConfiguration(configToDelete!!)
                }
                appViewModel.dismissDialog()
            }
        )
    }

    fun startEditing() {
        _mode.value = BlisterConfigMode.EDIT
        updateGridViewModel()
    }

    fun cancelEditing() {
        _mode.value = BlisterConfigMode.LIST
        _selectedConfiguration.value = null
        _gridViewModel.value = null
        _errorMessage.value = null
        clearEditForm()
        updateGridViewModel()
    }

    fun startCreate() {
        clearEditForm()

        _mode.value = BlisterConfigMode.CREATE
        _selectedConfiguration.value = null
        _gridViewModel.value = null
        _errorMessage.value = null
        _successMessage.value = null
        updateGridViewModel()
    }
    fun clearNewConfigForm() {
        _newConfigName.value = ""
        _newConfigRows.value = 4
        _newConfigFlipTime.value = false
        _newConfigFlipDays.value = false
        _newConfigStations.value = ""
    }

    fun clearEditForm() {
        _editConfigName.value = ""
        _editConfigRows.value = 4
        _editConfigFlipTime.value = false
        _editConfigFlipDays.value = false
        _editConfigStations.value = ""
    }

    fun dismissErrorMessage() {
        _errorMessage.value = null
    }

    fun dismissSuccessMessage() {
        _successMessage.value = null
    }

    fun addConfiguration() {
        viewModelScope.launch {
            try {
                // Validate input
                if (newConfigName.value.isBlank()) {
                    _errorMessage.value = "Konfigurationsname darf nicht leer sein"
                    return@launch
                }

                // Check if configuration already exists
                if (configurations.value.any { it.name == newConfigName.value }) {
                    _errorMessage.value = "Eine Konfiguration mit diesem Namen existiert bereits"
                    return@launch
                }

                // Parse stations
                val stationsList = if (newConfigStations.value.isBlank()) {
                    emptyList()
                } else {
                    newConfigStations.value.split(",").map { it.trim() }.filter { it.isNotBlank() }
                }

                // Create default column widths (7 columns)
                val defaultColumnWidths = List(7) { 180f }

                // Create row heights based on number of rows
                val defaultRowHeights = List(newConfigRows.value) { 190f }

                // Create new configuration
                val newConfig = BlisterConfiguration(
                    name = newConfigName.value,
                    columnWidth = defaultColumnWidths,
                    rowHeight = defaultRowHeights,
                    marginX = 62f,
                    marginY = 62f,
                    flipTime = newConfigFlipTime.value,
                    flipDays = newConfigFlipDays.value,
                    stations = stationsList
                )

                // Save configuration
                val updatedConfigs = configurations.value + newConfig
                appViewModel.saveBlisterConfigurations(updatedConfigs)

                // Clear form and refresh list
                clearNewConfigForm()
                refreshConfigurations()
                _mode.value = BlisterConfigMode.LIST
                updateGridViewModel()
                // TODO ? _successMessage.value = "Konfiguration '${newConfig.name}' erfolgreich hinzugefügt"
            } catch (e: Exception) {
                _errorMessage.value = "Fehler beim Hinzufügen der Konfiguration: ${e.message}"
            }
        }
    }

    fun updateConfiguration() {
        viewModelScope.launch {
            try {
                val selectedConfig = selectedConfiguration.value ?: return@launch

                // Validate input
                if (editConfigName.value.isBlank()) {
                    _errorMessage.value = "Konfigurationsname darf nicht leer sein"
                    return@launch
                }

                // Check if name changed and new name already exists
                if (editConfigName.value != selectedConfig.name &&
                    configurations.value.any { it.name == editConfigName.value }) {
                    _errorMessage.value = "Eine Konfiguration mit diesem Namen existiert bereits"
                    return@launch
                }

                // Parse stations
                val stationsList = if (editConfigStations.value.isBlank()) {
                    emptyList()
                } else {
                    editConfigStations.value.split(",").map { it.trim() }.filter { it.isNotBlank() }
                }

                // Create updated row heights if rows changed
                val updatedRowHeights = if (editConfigRows.value != selectedConfig.rowHeight.size) {
                    List(editConfigRows.value) { 190f }
                } else {
                    selectedConfig.rowHeight
                }

                // Create updated configuration
                val updatedConfig = selectedConfig.copy(
                    name = editConfigName.value,
                    rowHeight = updatedRowHeights,
                    flipTime = editConfigFlipTime.value,
                    flipDays = editConfigFlipDays.value,
                    stations = stationsList
                )

                // Save configuration
                val updatedConfigs = configurations.value.map {
                    if (it.name == selectedConfig.name) updatedConfig else it
                }
                appViewModel.saveBlisterConfigurations(updatedConfigs)

                // Clear form and refresh list
                cancelEditing()
                refreshConfigurations()
                _successMessage.value = "Konfiguration '${updatedConfig.name}' erfolgreich aktualisiert"
            } catch (e: Exception) {
                _errorMessage.value = "Fehler beim Aktualisieren der Konfiguration: ${e.message}"
            }
        }
    }

    fun removeConfiguration(config: BlisterConfiguration) {
        viewModelScope.launch {
            try {
                val updatedConfigs = configurations.value.filter { it.name != config.name }
                appViewModel.saveBlisterConfigurations(updatedConfigs)
                refreshConfigurations()
                _selectedConfiguration.value = null
                _mode.value = BlisterConfigMode.LIST
                _successMessage.value = "Konfiguration '${config.name}' erfolgreich gelöscht"
            } catch (e: Exception) {
                _errorMessage.value = "Fehler beim Löschen der Konfiguration: ${e.message}"
            }
        }
    }

    fun openCalibration(config: BlisterConfiguration) {
        val calibrationViewModel = CalibrationViewModel(appViewModel)
        calibrationViewModel.selectBlisterConfig(config)
        calibrationViewModel.present(appViewModel)
    }

    companion object {
        fun present(appViewModel: AppViewModel) {
            appViewModel.navigation.push(BlisterConfigurationManagementViewModel(appViewModel))
        }
    }
}
