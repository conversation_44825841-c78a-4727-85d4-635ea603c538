package de.fishbyte.mrblister.app.views.jobimport;

import androidx.lifecycle.ViewModel
import de.fishbyte.mrblister.app.AppViewModel
import de.fishbyte.mrblister.app.views.joblist.JobListViewModel
import de.fishbyte.mrblister.app.views.joblist.importBlisterFiles
import de.fishbyte.mrblister.services.LoggerService
import de.fishbyte.mrblister.services.SearchService
import java.io.File
import kotlin.math.log

public class JobImportViewModel(val appViewModel: AppViewModel, private val logger: LoggerService, private val searchService: SearchService) : ViewModel() {
    fun importJobs(selectedFiles: Set<File>) {
        appViewModel.importBlisterFiles(selectedFiles,logger,searchService)
        JobListViewModel.present(appViewModel)
    }

    companion object {
        fun present(appViewModel: AppViewModel, searchService: SearchService) {
            appViewModel.navigation.replaceAll(JobImportViewModel(appViewModel, appViewModel.logger, searchService))
        }
    }
}