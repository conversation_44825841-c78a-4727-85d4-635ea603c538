package de.fishbyte.mrblister.app.views.history

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

@Composable
fun DatePicker(
    viewModel: HistoryViewModel
) {
    val startDate by viewModel.startDate.collectAsState()
    val endDate by viewModel.endDate.collectAsState()

    var showStartDatePicker by remember { mutableStateOf(false) }
    var showEndDatePicker by remember { mutableStateOf(false) }

    val dateFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy")
    val today = LocalDate.now()
    val threeMonthsAgo = today.minusMonths(3)

    ElevatedCard(
        modifier = Modifier.width(280.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                "Zeitraum",
                style = MaterialTheme.typography.titleMedium
            )

            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = startDate?.format(dateFormatter) ?: "",
                    onValueChange = { },
                    readOnly = true,
                    modifier = Modifier.weight(1f),
                    label = { Text("Von") },
                    leadingIcon = {
                        Icon(
                            Icons.Default.ShoppingCart,
                            contentDescription = "Kalender",
                            modifier = Modifier.clickable { showStartDatePicker = true }
                        )
                    },
                    trailingIcon = {
                        if (startDate != null) {
                            IconButton(onClick = { viewModel.setStartDate(null) }) {
                                Icon(Icons.Default.Clear, "Löschen")
                            }
                        }
                    }
                )

                Text("bis")

                OutlinedTextField(
                    value = endDate?.format(dateFormatter) ?: "",
                    onValueChange = { },
                    readOnly = true,
                    modifier = Modifier.weight(1f),
                    label = { Text("Bis") },
                    leadingIcon = {
                        Icon(
                            Icons.Default.ShoppingCart,
                            contentDescription = "Kalender",
                            modifier = Modifier.clickable { showEndDatePicker = true }
                        )
                    },
                    trailingIcon = {
                        if (endDate != null) {
                            IconButton(onClick = { viewModel.setEndDate(null) }) {
                                Icon(Icons.Default.Clear, "Löschen")
                            }
                        }
                    }
                )
            }
        }
    }

    // Start Date Picker Dialog
    if (showStartDatePicker) {
        DatePickerDialog(
            onDismissRequest = { showStartDatePicker = false },
            onDateSelected = { selectedDate ->
                viewModel.setStartDate(selectedDate)
                showStartDatePicker = false
            },
            minDate = threeMonthsAgo,
            maxDate = endDate?.let { if (it.isBefore(today)) it else today } ?: today,
            initialDate = startDate ?: today
        )
    }

    // End Date Picker Dialog
    if (showEndDatePicker) {
        DatePickerDialog(
            onDismissRequest = { showEndDatePicker = false },
            onDateSelected = { selectedDate ->
                viewModel.setEndDate(selectedDate)
                showEndDatePicker = false
            },
            minDate = startDate ?: threeMonthsAgo,
            maxDate = today,
            initialDate = endDate ?: today
        )
    }
}

@Composable
fun DatePickerDialog(
    onDismissRequest: () -> Unit,
    onDateSelected: (LocalDate) -> Unit,
    minDate: LocalDate,
    maxDate: LocalDate,
    initialDate: LocalDate
) {
    var selectedDate by remember { mutableStateOf(initialDate) }

    AlertDialog(
        onDismissRequest = onDismissRequest,
        title = { Text("Datum auswählen") },
        text = {
            Column {
                // Simple date picker implementation
                // In a real app, you might want to use a calendar view here
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    // Year picker
                    val years = (minDate.year..maxDate.year).toList()
                    DropdownMenuItem(text = { Text("Jahr") }, onClick = {})

                    // Month picker
                    val months = (1..12).map { it.toString() }
                    DropdownMenuItem(text = { Text("Monat") }, onClick = {})

                    // Day picker
                    val daysInMonth = selectedDate.month.length(selectedDate.isLeapYear)
                    val days = (1..daysInMonth).map { it.toString() }
                    DropdownMenuItem(text = { Text("Tag") }, onClick = {})
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = { onDateSelected(selectedDate) }
            ) {
                Text("Auswählen")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismissRequest
            ) {
                Text("Abbrechen")
            }
        }
    )
}
