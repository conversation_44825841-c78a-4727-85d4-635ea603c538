package de.fishbyte.mrblister.app.camera

import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toPainter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.camera.CameraViewState.*
import de.fishbyte.mrblister.app.views.job.BlisterJobViewModel
import de.fishbyte.mrblister.components.Components


@Composable
fun cameraView(jobViewModel: BlisterJobViewModel) {

    val viewModel = jobViewModel.cameraViewModel.value

    val state = viewModel.state.collectAsState()
    val message = viewModel.message.collectAsState()
    val autoConfirmProgress = viewModel.autoConfirmProgress.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.requestPicture()
    }

    ElevatedCard(
        elevation = CardDefaults.cardElevation(defaultElevation = 6.dp),
        modifier = Modifier.fillMaxWidth().height(600.dp)) {
        Column(modifier = Modifier.padding(16.dp)) {
            /*
            val title = if (jobViewModel.status.value == BlisterJobViewModelStatus.TakeMedicationPicture) {
                "Foto von Medikation wird erstellt"
            } else {
                "Kontrollfoto für Blister erstellen"
            }

            Text(
                title,
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.headlineMedium
            )
            */

            Box(
                modifier = Modifier.padding(8.dp).fillMaxWidth().weight(1.0f), contentAlignment = Alignment.Center
            ) {
                        when (state.value) {
                            pending,
                            initialize,
                            ready -> {
                                    Text(message.value, style = MaterialTheme.typography.headlineMedium)
                            }

                            captured,
                            rolling -> {

                                val painter = viewModel.currentImage.value?.toPainter()!!

                                    Image(
                                        painter,
                                        "An image captured from the camera",
                                        modifier = Modifier.fillMaxSize(), //Modifier.size(800.dp),
                                        contentScale = ContentScale.Fit
                                    )

                                    if (viewModel.autoConfirmSeconds > 0 && autoConfirmProgress.value < 2) {
                                        CircularProgressIndicator(
                                            progress = { autoConfirmProgress.value },
                                            strokeWidth = 20.dp,
                                            modifier = Modifier
                                                .size(400.dp)
                                                .align(Alignment.Center)
                                                .alpha(0.4f)
                                                .padding(16.dp),
                                            color = MaterialTheme.colorScheme.primary,
                                        )
                                    }
                            }

                            error -> {
                                    Text(
                                        message.value,
                                        modifier = Modifier.fillMaxSize(),
                                        style = MaterialTheme.typography.headlineMedium
                                    )
                            }
                        }
                    }
                }
            }


}

@Composable
fun RowScope.cameraViewFooterElements(jobViewModel: BlisterJobViewModel)
{
    val viewModel = jobViewModel.cameraViewModel.value
    val state = viewModel.state.collectAsState()
    when (state.value) {
        captured, rolling -> {
            if( !viewModel.rollingMode.value ) {
                Components.secondaryButton("Wiederholen", onClick = {
                    viewModel.requestPicture()
                }, modifier = Modifier.weight(2f))
            }
            Spacer(modifier = Modifier.width(8.dp))
            Components.primaryButton("Akzeptiert", onClick = {
                viewModel.stopAutoConfirm()
                jobViewModel.acceptPicture()
            }, modifier = Modifier.weight(3f))
        }

        error -> {
            Components.secondaryButton("Abbrechen", onClick = {
                jobViewModel.finishMedication()
            }, modifier = Modifier.weight(2f))
            Spacer(modifier = Modifier.width(8.dp))
            Components.primaryButton("Wiederholen",
                onClick = { viewModel.requestPicture() },
                modifier = Modifier.weight(3f))
        }

        else -> {

        }
    }
}
