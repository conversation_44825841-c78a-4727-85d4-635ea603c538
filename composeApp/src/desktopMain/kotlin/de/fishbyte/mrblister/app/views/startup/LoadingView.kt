package de.fishbyte.mrblister.app.views.startup

import androidx.compose.foundation.layout.*
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.AppViewModel
import de.fishbyte.mrblister.app.Settings
import de.fishbyte.mrblister.app.license.LicenseValidator
import de.fishbyte.mrblister.app.views.joblist.loadBlisterJobs
import de.fishbyte.mrblister.app.views.login.LoginViewModel
import de.fishbyte.mrblister.app.views.tools.formatShortDate
import de.fishbyte.mrblister.model.UserRepository
import de.fishbyte.mrblister.model.loadJobStatus
import de.fishbyte.mrblister.model.loadSettings
import de.fishbyte.mrblister.services.LoggerService
import de.fishbyte.mrblister.services.SearchService
import kotlinx.coroutines.launch
import org.koin.compose.getKoin
import java.io.File
import kotlin.math.log

enum class LoadingState {
    LOADING_CONFIG,
    VALIDATING_LICENSE,
    LOADING_USERS,
    CONNECT_SCANNER,
    LOADING_BLISTER_CONFIG,
    LOADING_BLISTER,
    LOADING_JOBS_STATUS,
    CLEANUP,
    ERROR,
    FINISHED
}

@Composable
fun LoadingView(
    settingsPath: String?,
    onLoadingComplete: () -> Unit = {}
) {
    var currentState by remember { mutableStateOf(LoadingState.LOADING_CONFIG) }
    var errorMessage by remember { mutableStateOf<String?>(null) }

    val coroutineScope = rememberCoroutineScope()

    val appViewModel = getKoin().get<AppViewModel>()
    val logger = appViewModel.logger
    val settings = appViewModel.settings
    val searchService = getKoin().get<SearchService>()

    LaunchedEffect(Unit) {
        coroutineScope.launch {
            try {
                currentState = LoadingState.LOADING_CONFIG
                loadConfiguration(settingsPath, settings)

                currentState = LoadingState.VALIDATING_LICENSE
                if (!LicenseValidator.isLicenseValid()) {
                    val expirationDate = formatShortDate(LicenseValidator.getLicenseExpirationDate())
                    throw Exception("Die Lizenz ist abgelaufen. Gültig bis: $expirationDate")
                }

                currentState = LoadingState.LOADING_USERS
                loadUsers(appViewModel.userRepository, appViewModel.settings)

                if(settings.scannerPort?.isNotEmpty() == true) {
                    currentState = LoadingState.CONNECT_SCANNER
                    appViewModel.connectScanner()
                }

                currentState = LoadingState.LOADING_BLISTER_CONFIG
                appViewModel.loadBlisterConfig()

                currentState = LoadingState.LOADING_JOBS_STATUS
                appViewModel.loadJobStatus()

                currentState = LoadingState.LOADING_BLISTER
                appViewModel.loadBlisterJobs(logger, searchService)

                currentState = LoadingState.CLEANUP
                appViewModel.cleanup()

                currentState = LoadingState.FINISHED

                LoginViewModel.present(appViewModel)
            } catch (e: Exception) {
                e.printStackTrace()
                errorMessage = e.message
                currentState = LoadingState.ERROR
            }
        }
    }

    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        if (currentState != LoadingState.FINISHED && currentState != LoadingState.ERROR) {
            CircularProgressIndicator(
                modifier = Modifier.size(48.dp),
                strokeWidth = 4.dp
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = when(currentState) {
                LoadingState.LOADING_CONFIG -> "Lade Konfiguration..."
                LoadingState.VALIDATING_LICENSE -> "Prüfe Lizenz..."
                LoadingState.LOADING_USERS -> "Lade Benutzer..."
                LoadingState.CONNECT_SCANNER -> "Verbinde Scanner..."
                LoadingState.LOADING_BLISTER_CONFIG -> "Lade Blister Konfiguration..."
                LoadingState.LOADING_BLISTER -> "Lade Blister..."
                LoadingState.LOADING_JOBS_STATUS -> "Lade Job Status..."
                LoadingState.CLEANUP -> "Aufräumen..."
                LoadingState.ERROR -> "Fehler beim Laden\n${errorMessage}"
                LoadingState.FINISHED -> "Start abgeschlossen"
            },
            modifier = Modifier.fillMaxWidth(),
            textAlign = androidx.compose.ui.text.style.TextAlign.Center
        )
    }
}

fun loadConfiguration(settingsPath: String?, settings: Settings) {

    val directoryPath = if( settingsPath.isNullOrBlank() ) {
        File(System.getProperty("user.dir"))
    } else {
        File(settingsPath)
    }

    if( !directoryPath.isDirectory ) {
        throw Exception("Das Konfigurationsverzeichnis konnte nicht gefunden werden\n${settingsPath}")
    }

    val path = File(directoryPath, "settings.xml").path
    try {
        loadSettings( path, settings)
    } catch (e: Exception) {
        throw Exception("Fehler beim Laden der Konfiguration\n$path\n${e.message}")
    }
}

private suspend fun loadUsers(users: UserRepository, settings: Settings) {
    try {
        if( !File(settings.userFilePath).exists() ) {
            throw Exception("Die Benutzerkonfigurationsdatei konnte nicht gefunden werden\n" +
                    "${settings.userFilePath}")
        }

        UserRepository.loadUsers(users, settings)

        if( users.getUsers().isEmpty() ) {
            throw Exception("Die Benutzerkonfiguration enthält keine Benutzer\n" +
                    "${settings.userFilePath}")
        }
    } catch (e: Exception) {
        throw Exception("Fehler beim Laden der Benutzerkonfiguration\n"+
                "${settings.userFilePath}\n${e.message}")
    }
}
