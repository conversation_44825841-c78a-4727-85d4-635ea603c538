import de.fishbyte.mrblister.model.Medication
import java.time.DayOfWeek
import java.time.LocalTime
import java.time.format.DateTimeFormatter

class MedicationDosageModel( var weekDayRanges: List<ClosedRange<DayOfWeek>>, var dosages: Map<LocalTime, Double>) {

    fun canMergeDays( other: MedicationDosageModel ) : Boolean {

        return dosages == other.dosages
        /*
        // can merge if dosages are the same and weekDayRange borders each other
        if( (weekDayRange.start != DayOfWeek.MONDAY && weekDayRange.start == (other.weekDayRange.endInclusive + 1)) ||
            (weekDayRange.endInclusive != DayOfWeek.SUNDAY && weekDayRange.endInclusive == (other.weekDayRange.start - 1) )) {
            return dosages == other.dosages
        }

        // or if weekDayRange is the same and dosages do not override each other
        if( weekDayRange == other.weekDayRange ) {
            val conflictingTimes = dosages.keys.intersect(other.dosages.keys)
            if (conflictingTimes.isNotEmpty()) {
                // For any shared times, verify the dosage values match
                for (time in conflictingTimes) {
                    if (dosages[time] != other.dosages[time]) {
                        return false
                    }
                }
            }
            return true
        }

        return false

         */
    }

    fun mergeWith(other: MedicationDosageModel): MedicationDosageModel {

        // check if dosages and other.dosages doe not override each other
        // if they do, throw an exception
        // if they don't, add them to the result
        val conflictingTimes = dosages.keys.intersect(other.dosages.keys)
        if (conflictingTimes.isNotEmpty()) {
            // For any shared times, verify the dosage values match
            for (time in conflictingTimes) {
                if (dosages[time] != other.dosages[time]) {
                    throw IllegalArgumentException("Conflicting dosages for time $time")
                }
            }
        }

        return MedicationDosageModel(

            weekDayRanges = weekDayRanges + other.weekDayRanges,
            dosages = dosages)
    }

    override fun toString(): String {
        val sb = StringBuilder()
        for( weekDayRange in weekDayRanges ) {
            if( sb.isNotEmpty() )
                sb.append(",")
            if (weekDayRange.start == weekDayRange.endInclusive) {
                sb.append(weekDayRange.start.toString().substring(0, 2))
            } else {
                sb.append(weekDayRange.start.toString().substring(0, 2))
                sb.append("-")
                sb.append(weekDayRange.endInclusive.toString().substring(0, 2))
            }
        }

        val formatter = DateTimeFormatter.ofPattern("HH:mm")

        for( time in dosages.keys.sorted() ) {
            sb.append("_")
            sb.append(time.format(formatter))
            sb.append(":")
            sb.append(dosages[time])
        }

        return sb.toString()
    }

    fun getLocalizedWeekDays(): String {
        val sb = StringBuilder()

        for( weekDayRange in weekDayRanges ) {
            if (sb.isNotEmpty())
                sb.append(",")
            if (weekDayRange.start == weekDayRange.endInclusive) {
                sb.append(getLocalizedWeekDay(weekDayRange.start))
            } else {
                sb.append(getLocalizedWeekDay(weekDayRange.start))
                        sb.append("-")
                sb.append(getLocalizedWeekDay(weekDayRange.endInclusive))
            }
        }
        return sb.toString()
    }

    fun isAllWeek() : Boolean {
        if( weekDayRanges.size ==1 ) {
            val range = weekDayRanges.first()
            if (range.start == DayOfWeek.MONDAY && range.endInclusive == DayOfWeek.SUNDAY) {
                return true
            }
        }
        return false
    }

    private fun getLocalizedWeekDay( weekDay: DayOfWeek): String {
        return when(weekDay) {
            DayOfWeek.MONDAY -> "Mo"
            DayOfWeek.TUESDAY -> "Di"
            DayOfWeek.WEDNESDAY -> "Mi"
            DayOfWeek.THURSDAY -> "Do"
            DayOfWeek.FRIDAY -> "Fr"
            DayOfWeek.SATURDAY -> "Sa"
            DayOfWeek.SUNDAY -> "So"
        }
    }

    companion object {
        fun fromMedication(medication: Medication): MedicationDosageModel {
            return MedicationDosageModel(
                weekDayRanges = listOf(medication.takingDate.dayOfWeek..medication.takingDate.dayOfWeek),
                dosages = mapOf( Pair(medication.takingTime, medication.dosage) )
            )
        }

        fun mergeDays(meds: List<MedicationDosageModel>): List<MedicationDosageModel> {
            if (meds.isEmpty()) return emptyList()

            // First, group medications by their day ranges
            val groupedByDays = meds.groupBy { model ->
                // Create a signature of the day ranges for grouping
                model.weekDayRanges.sortedBy { it.start.value }
                    .joinToString(",") { "${it.start.value}-${it.endInclusive.value}" }
            }

            val result = mutableListOf<MedicationDosageModel>()

            // For each group of medications with the same day ranges
            for ((_, sameRangeModels) in groupedByDays) {
                // Try to merge dosages for the same day ranges
                val mergedDosages = mutableMapOf<LocalTime, Double>()
                var canMerge = true

                // Check if dosages can be merged (no conflicts)
                for (model in sameRangeModels) {
                    for ((time, dosage) in model.dosages) {
                        if (mergedDosages.containsKey(time) && mergedDosages[time] != dosage) {
                            // Conflict found - same time but different dosage
                            canMerge = false
                            break
                        }
                        mergedDosages[time] = dosage
                    }
                    if (!canMerge) break
                }

                if (canMerge) {
                    // Create a single model with combined dosages
                    result.add(MedicationDosageModel(
                        weekDayRanges = sameRangeModels.first().weekDayRanges,
                        dosages = mergedDosages
                    ))
                } else {
                    // Can't merge, add all models separately
                    result.addAll(sameRangeModels)
                }
            }

            // Now try to merge models with the same dosage patterns but different day ranges
            return mergeModelsByDosage(result)
        }

        private fun mergeModelsByDosage(models: List<MedicationDosageModel>): List<MedicationDosageModel> {
            if (models.size <= 1) return models

            // Group by dosage patterns
            val groupedByDosage = models.groupBy { it.dosages }

            val result = mutableListOf<MedicationDosageModel>()

            // For each group with the same dosage pattern
            for ((dosages, samePatternModels) in groupedByDosage) {
                // Merge day ranges
                val mergedRanges = mergeWeekDayRanges(samePatternModels.flatMap { it.weekDayRanges })

                // Create a new model with merged day ranges
                result.add(MedicationDosageModel(
                    weekDayRanges = mergedRanges.sortedBy { it.start },
                    dosages = dosages
                ))
            }

            return result
        }

        private fun mergeWeekDayRanges(ranges: List<ClosedRange<DayOfWeek>>): List<ClosedRange<DayOfWeek>> {
            if (ranges.isEmpty()) return emptyList()

            // Convert DayOfWeek ranges to Int ranges for easier manipulation
            val dayRanges = ranges.map { it.start.value..it.endInclusive.value }

            // Sort ranges by start day
            val sortedRanges = dayRanges.sortedBy { it.start }

            // Merge overlapping or adjacent ranges
            val mergedRanges = mutableListOf<IntRange>()
            var currentRange = sortedRanges.first()

            for (i in 1 until sortedRanges.size) {
                val nextRange = sortedRanges[i]

                // If ranges overlap or are adjacent (considering week wrapping)
                if (currentRange.endInclusive >= nextRange.start - 1 ||
                    (currentRange.endInclusive == 7 && nextRange.start == 1)) {
                    // Merge ranges
                    currentRange = currentRange.start..maxOf(currentRange.endInclusive, nextRange.endInclusive)
                } else {
                    // Add the current range to results and start a new one
                    mergedRanges.add(currentRange)
                    currentRange = nextRange
                }
            }

            // Add the last range
            mergedRanges.add(currentRange)

            // Convert back to DayOfWeek ranges
            return mergedRanges.map {
                DayOfWeek.of(it.start)..DayOfWeek.of(it.endInclusive)
            }
        }

    }
}
