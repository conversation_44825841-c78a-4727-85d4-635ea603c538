package de.fishbyte.mrblister.app

class ScannerService {

    // reads a barcode from a scanner that is connected via a USB CDC class device
    // returns the barcode as a string
    fun readBarcode(): String {
        // open serial port and read string

        return "1234567890"
    }
}

fun String.matchBarcode( barcode: String) : <PERSON><PERSON>an {
    try {
        return this.toLong() == (barcode.removePrefix("-")).toLong()
    } catch (e: Exception) {
        return this.equals(barcode)
    }
}