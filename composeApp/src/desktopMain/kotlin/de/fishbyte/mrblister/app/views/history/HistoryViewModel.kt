package de.fishbyte.mrblister.app.views.history

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import de.fishbyte.mrblister.app.AppViewModel
import de.fishbyte.mrblister.app.views.KeyboardHandler
import de.fishbyte.mrblister.app.views.job.MedicationSummary
import de.fishbyte.mrblister.model.*
import de.fishbyte.mrblister.services.SearchResult
import de.fishbyte.mrblister.services.SearchService
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import org.koin.compose.getKoin
import java.awt.image.BufferedImage
import java.io.File
import java.time.LocalDate
import java.util.*
import javax.imageio.ImageIO

class HistoryViewModel(val appViewModel: AppViewModel, private val search: SearchService) : ViewModel(), KeyboardHandler {

    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()

    private val _filteredJobs = MutableStateFlow<List<SearchResult>>(emptyList())
    val filteredJobs: StateFlow<List<SearchResult>> = _filteredJobs.asStateFlow()

    // Add filters
    private val _startDate = MutableStateFlow<LocalDate?>(null)
    val startDate: StateFlow<LocalDate?> = _startDate.asStateFlow()

    private val _endDate = MutableStateFlow<LocalDate?>(null)
    val endDate: StateFlow<LocalDate?> = _endDate.asStateFlow()

    private val _selected = MutableStateFlow<SearchResult?>(null)
    val selected: StateFlow<SearchResult?> = _selected.asStateFlow()

    private val _selectedJob = MutableStateFlow<BlisterJob?>(null)
    val selectedJob: StateFlow<BlisterJob?> = _selectedJob.asStateFlow()

    private val _medications = MutableStateFlow<List<MedicationSummary>>(emptyList())
    val medications: StateFlow<List<MedicationSummary>> = _medications.asStateFlow()

    private val  _currentImage = MutableStateFlow<BufferedImage?>(null)
    val currentImage: StateFlow<BufferedImage?> get() = _currentImage.asStateFlow()

    private var _imageNames = emptyMap<String, String>()
    private val _images = mutableMapOf<String, BufferedImage>()

    init {
        // Combine all search/filter parameters and update results
        viewModelScope.launch {
            combine(
                searchQuery,
                _startDate,
                _endDate
            ) { query, start, end ->
                searchJobs(query, start, end)
            }.collect { results ->
                _filteredJobs.value = results
            }
        }
    }

    override fun handleBarcode(key: String) {
        // Implement the handleBarcode function
    }
    private fun searchJobs(
        query: String,
        startDate: LocalDate?,
        endDate: LocalDate?
    ): List<SearchResult> {

        if( query.isNullOrBlank() ) {
            return search.searchLast()
        }
        return search.searchIndex(query)
    }

    fun setStartDate(date: LocalDate?) {
        _startDate.value = date
    }

    fun setEndDate(date: LocalDate?) {
        _endDate.value = date
    }

    fun updateSearch(it: String) {
        try {
            _searchQuery.value = it
            if( it.isNullOrBlank() ) {
                _filteredJobs.value = search.searchLast()
            } else {
                _filteredJobs.value = search.searchIndex(it)
            }
        } catch (e: Exception) {
            _filteredJobs.value = emptyList()
        }
    }

    fun present(appViewModel: AppViewModel) {
       appViewModel.navigation.push(this)
    }

    fun showResult(result: SearchResult) {
        _selected.value = result

        val directory = File(result.directory)

        val job = BlisterJob.load(File(directory, "job_${result.id}.job.json").path)
        _selectedJob.value = job

        _medications.value = job.drugs.stream().map { drug ->
            val medications = job.medications.filter { it.drugID == drug.id }.toList()
            val dosage = medications.sumOf { it.dosage }

            MedicationSummary(drug, medications, dosage, MedicationStatus.Completed) //status?.getMedicationStatus(drugID)?.status ?: MedicationStatus.Pending)
        }.toList()


        _images.clear()
        if( directory.isDirectory ) {
            val prefix = "image_${result.id}"
            val files = directory.listFiles { file -> file.isFile && file.name.endsWith(".png") && file.name.startsWith(prefix) }

            val names = mutableMapOf<String,String>()

            for( file in files ) {

                val med = file.name.substring(prefix.length)

                if( med.startsWith(".png")) {
                    names[job.localID] = file.path
                } else if( med.startsWith("_")) {
                    val id = med.substring(1).removeSuffix(".png")
                    names[id] = file.path
                }
            }

            _imageNames = names

            names[job.localID]?.let { it ->
                _images[job.localID] = loadBufferedImage(File(it))
            }
        }

        _currentImage.value = _images[job.localID]
    }

    fun loadBufferedImage(file: File): BufferedImage {
        val image = ImageIO.read(file)
        return image
    }

    fun deselect() {
        _selected.value = null
        //_images.value = emptyList()
        _currentImage.value = null
    }

    fun selectDrug(drug: Drug) {
        val medication = _selectedJob.value?.medications?.filter { it.drugID == drug.id }?.firstOrNull()

        val image = if( _images[drug.id] != null ) {
            _images[drug.id]
        } else {
            if( _imageNames[drug.id] != null ) {
                _images[drug.id] = loadBufferedImage(File(_imageNames[drug.id]!!))
                _images[drug.id]
            }
            else if( _images.isNotEmpty()){
                _images[_selectedJob.value?.localID]
            } else {
                null as BufferedImage?
            }
        }

        _currentImage.value = image
    }
}