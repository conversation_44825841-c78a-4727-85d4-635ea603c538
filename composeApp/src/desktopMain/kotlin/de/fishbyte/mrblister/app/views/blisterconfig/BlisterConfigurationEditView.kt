package de.fishbyte.mrblister.app.views.blisterconfig

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.AppViewModel
import org.koin.mp.KoinPlatform.getKoin

@Composable
fun BlisterConfigurationEditView( viewModel: BlisterConfigurationManagementViewModel ) {
    val appViewModel = getKoin().get<AppViewModel>()

    val configurations by viewModel.configurations.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    val successMessage by viewModel.successMessage.collectAsState()
    val selectedConfiguration by viewModel.selectedConfiguration.collectAsState()
    val mode by viewModel.mode.collectAsState()

    Column {
        // Add/Edit configuration form
        if (mode == BlisterConfigMode.EDIT) {
            EditConfigurationForm(
                viewModel = viewModel,
                modifier = Modifier.weight(1f)
            )
        } else {
            AddConfigurationForm(
                viewModel = viewModel,
                modifier = Modifier.weight(1f)
            )
        }
    }
}


@Composable
fun AddConfigurationForm(
    viewModel: BlisterConfigurationManagementViewModel,
    modifier: Modifier = Modifier
) {
    val newConfigName by viewModel.newConfigName.collectAsState()
    val newConfigRows by viewModel.newConfigRows.collectAsState()
    val newConfigFlipTime by viewModel.newConfigFlipTime.collectAsState()
    val newConfigFlipDays by viewModel.newConfigFlipDays.collectAsState()
    val newConfigStations by viewModel.newConfigStations.collectAsState()

    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Text(
                "Neue Konfiguration hinzufügen",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Form fields
            OutlinedTextField(
                value = newConfigName,
                onValueChange = viewModel::updateNewConfigName,
                label = { Text("Konfigurationsname") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )

            Spacer(modifier = Modifier.height(16.dp))

            OutlinedTextField(
                value = newConfigRows.toString(),
                onValueChange = { value ->
                    value.toIntOrNull()?.let { viewModel.updateNewConfigRows(it) }
                },
                label = { Text("Anzahl Zeilen") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = newConfigFlipTime,
                    onCheckedChange = viewModel::updateNewConfigFlipTime
                )
                Text(
                    "Zeit umgedreht",
                    modifier = Modifier.padding(start = 8.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = newConfigFlipDays,
                    onCheckedChange = viewModel::updateNewConfigFlipDays
                )
                Text(
                    "Tage umgedreht",
                    modifier = Modifier.padding(start = 8.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            OutlinedTextField(
                value = newConfigStations,
                onValueChange = viewModel::updateNewConfigStations,
                label = { Text("Stationen (kommagetrennt)") },
                modifier = Modifier.fillMaxWidth(),
                placeholder = { Text("z.B. Station 1, Station 2, Station 3") }
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Submit button
            Button(
                onClick = viewModel::addConfiguration,
                modifier = Modifier.align(Alignment.End)
            ) {
                Text("Konfiguration hinzufügen")
            }
        }
    }
}

@Composable
fun EditConfigurationForm(
    viewModel: BlisterConfigurationManagementViewModel,
    modifier: Modifier = Modifier
) {
    val editConfigName by viewModel.editConfigName.collectAsState()
    val editConfigRows by viewModel.editConfigRows.collectAsState()
    val editConfigFlipTime by viewModel.editConfigFlipTime.collectAsState()
    val editConfigFlipDays by viewModel.editConfigFlipDays.collectAsState()
    val editConfigStations by viewModel.editConfigStations.collectAsState()

    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Text(
                "Konfiguration bearbeiten",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Form fields
            OutlinedTextField(
                value = editConfigName,
                onValueChange = viewModel::updateEditConfigName,
                label = { Text("Konfigurationsname") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )

            Spacer(modifier = Modifier.height(16.dp))

            OutlinedTextField(
                value = editConfigRows.toString(),
                onValueChange = { value ->
                    value.toIntOrNull()?.let { viewModel.updateEditConfigRows(it) }
                },
                label = { Text("Anzahl Zeilen") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = editConfigFlipTime,
                    onCheckedChange = viewModel::updateEditConfigFlipTime
                )
                Text(
                    "Zeit umgedreht",
                    modifier = Modifier.padding(start = 8.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = editConfigFlipDays,
                    onCheckedChange = viewModel::updateEditConfigFlipDays
                )
                Text(
                    "Tage umgedreht",
                    modifier = Modifier.padding(start = 8.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            OutlinedTextField(
                value = editConfigStations,
                onValueChange = viewModel::updateEditConfigStations,
                label = { Text("Stationen (kommagetrennt)") },
                modifier = Modifier.fillMaxWidth(),
                placeholder = { Text("z.B. Station 1, Station 2, Station 3") }
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Action buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedButton(
                    onClick = viewModel::cancelEditing,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("Abbrechen")
                }
                Button(
                    onClick = viewModel::updateConfiguration,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("Speichern")
                }
            }
        }
    }
}