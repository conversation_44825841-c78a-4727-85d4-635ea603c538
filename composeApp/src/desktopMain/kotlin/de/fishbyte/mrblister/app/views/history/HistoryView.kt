import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toPainter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.AppViewModel
import de.fishbyte.mrblister.app.Settings
import de.fishbyte.mrblister.app.theme.AppTheme
import de.fishbyte.mrblister.app.views.blisterconfig.BlisterConfigMode
import de.fishbyte.mrblister.app.views.history.HistoryViewModel
import de.fishbyte.mrblister.app.views.job.JobHeaderView
import de.fishbyte.mrblister.app.views.job.JobInfoView
import de.fishbyte.mrblister.app.views.job.medicationView2
import de.fishbyte.mrblister.app.views.joblist.JobListViewModel
import de.fishbyte.mrblister.app.views.tools.formatShortDate
import de.fishbyte.mrblister.app.views.tools.formatShortDateTime
import de.fishbyte.mrblister.app.views.tools.join
import de.fishbyte.mrblister.components.Components
import de.fishbyte.mrblister.components.RootSplitViewComponent
import de.fishbyte.mrblister.model.getMedicationStatus
import de.fishbyte.mrblister.services.SearchResult
import org.koin.mp.KoinPlatform.getKoin

@Composable
fun HistoryView(viewModel: HistoryViewModel) {

    val blisterWidth = getKoin().get<Settings>().blisterWidth.dp
    val theme = getKoin().get<AppTheme>()
    val appViewModel = getKoin().get<AppViewModel>()
    val selected = viewModel.selected.collectAsState()

    if( selected.value == null ) {
        SearchUI(viewModel,blisterWidth,theme,appViewModel)
    } else {
        ResultUI(selected.value!!,viewModel,blisterWidth,theme,appViewModel)
    }
}

@Composable
fun SearchUI(viewModel: HistoryViewModel, blisterWidth: Dp, theme: AppTheme, appViewModel: AppViewModel)
{
    RootSplitViewComponent( footer = {
        Components.secondaryButton(
            "Zurück",
            { JobListViewModel.present(appViewModel) })
    }, content = {

        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = {
                JobListViewModel.present(appViewModel)
            }) {
                Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Zurück")
            }
            Text(
                "Verlauf",
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.padding(start = 8.dp)
            )
        }
        // Search bar
        OutlinedTextField(
            value = viewModel.searchQuery.collectAsState().value,
            onValueChange = { viewModel.updateSearch(it) },
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("Suche nach Patient, Medikament...") }
        )

        // Filters row
        Row(
            modifier = Modifier.fillMaxWidth().padding(vertical = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Date picker
            //DatePicker()

            // Status dropdown
            //FilterDropdown()

            // Patient filter
            //PatientFilter()
        }

        Spacer(modifier = theme.vertical_spacing_xl)

        Box(modifier = Modifier.weight(1f)) {
            // Results list
            val listState = rememberLazyListState()

            val results = viewModel.filteredJobs.collectAsState()
            if (results.value.isEmpty()) {
                Text("Keine Ergebnisse")
            } else {

                LazyColumn(state = listState) {
                    items(results.value.size) { index ->
                        val result = results.value[index]
                        JobHistoryItem(result, { viewModel.showResult(result) } )
                        Spacer(
                            modifier = theme.vertical_spacing_m
                        )
                    }
                }
            }

            VerticalScrollbar(
                modifier = Modifier.align(Alignment.CenterEnd).fillMaxHeight(),
                adapter = rememberScrollbarAdapter(listState)
            )

        }
    }, blisterContent = {

    })
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun JobHistoryItem(result: SearchResult, onClick: () -> Unit) {
    Surface(
        shape = MaterialTheme.shapes.medium,
        modifier = Modifier.fillMaxWidth().clickable(onClick = onClick)
    ) {
        ListItem(
            headlineContent = {
                Text( result.name )
            },
            supportingContent = { Text(join( result.birthday ,result.nursingHome)) },
            trailingContent = { if( result.index?.isNotEmpty() == true ) Text(result.index) },
            overlineContent = { Text( formatShortDateTime(result.completed)) }
        )
    }
}

@Composable
fun ResultUI(
    SearchResult: SearchResult,
    viewModel: HistoryViewModel,
    blisterWidth: Dp,
    theme: AppTheme,
    appViewModel: AppViewModel
) {

    val image = viewModel.currentImage.collectAsState()
    val job = viewModel.selectedJob.collectAsState()
    val medications = viewModel.medications.collectAsState()

    RootSplitViewComponent( content = {

        if (job.value != null) {
            JobHeaderView(job.value!!)
            Spacer(modifier = theme.vertical_spacing_xl)
        }

        Box(modifier = Modifier.weight(1f)) {  // Changed to Box with weight

            Column(modifier = Modifier.fillMaxSize())
            {
                medicationView2(medications.value,
                    { summary ->
                        viewModel.appViewModel.getOrCreateJobStatus(job.value!!).getMedicationStatus(summary.drug.id)
                    },
                    { drug ->
                        viewModel.selectDrug(drug)
                    })
            }
        }

        Column(modifier = Modifier.fillMaxWidth()) {
            Spacer(modifier = theme.vertical_spacing_xl)
            JobInfoView(job.value!!)
        }

    }, footer = {
        Spacer(modifier = Modifier.height(8.dp))

        Row(modifier = Modifier.fillMaxWidth().height(64.dp).padding(8.dp)) {
            Components.secondaryButton(
                "Zurück",
                { viewModel.deselect() })
        }
    }, blisterContent = {
        if( image.value != null ) {
            val painter = image.value!!.toPainter()

            Image(
                painter,
                "An image captured from the camera",
                modifier = Modifier.fillMaxSize(), //Modifier.size(800.dp),
                contentScale = ContentScale.Fit
            )
        }
    })
}



