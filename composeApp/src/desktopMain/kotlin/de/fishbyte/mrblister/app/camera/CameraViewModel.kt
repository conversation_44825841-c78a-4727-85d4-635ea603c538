package de.fishbyte.mrblister.app.camera

import androidx.lifecycle.ViewModel
import com.github.sarxos.webcam.Webcam
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.awt.image.BufferedImage

// Add interface for auto-confirm capability
interface AutoConfirmable {
    fun acceptPicture()
}

class CameraViewModel(var autoConfirmSeconds: Int = 0, private var autoConfirm: AutoConfirmable? = null) : ViewModel() {
    // Add new state flow for auto-confirm progress
    private val _autoConfirmProgress = MutableStateFlow(0f)
    val autoConfirmProgress: StateFlow<Float> get() = _autoConfirmProgress.asStateFlow()

    private val _state = MutableStateFlow(CameraViewState.pending)
    val state: StateFlow<CameraViewState> get() = _state.asStateFlow()

    private val  _currentImage = MutableStateFlow<BufferedImage?>(null)
    val currentImage: StateFlow<BufferedImage?> get() = _currentImage.asStateFlow()

    private val _message = MutableStateFlow("Warten auf Kamera")
    val message: StateFlow<String> get() = _message.asStateFlow()

    private var _imageRequested = false

    private val _rollingMode = MutableStateFlow(false)
    val rollingMode: StateFlow<Boolean> get() = _rollingMode.asStateFlow()

    fun setRollingMode( rollingMode: Boolean ) {
        _rollingMode.value = rollingMode
    }

    fun update() {
        if( _state.value == CameraViewState.pending ) {
            _autoConfirmProgress.value = 2f
            try {
                val webcam = Webcam.getDefault()

                if( webcam == null ) {
                    _state.value = CameraViewState.error
                    _message.value = "Keine Kamera gefunden, bitte Kamera aktivieren"
                } else {
                    webcam.open()
                    _state.value = CameraViewState.initialize
                }
            } catch (e: Exception) {
                print(e.toString())
                println(e.stackTraceToString())
                _state.value = CameraViewState.error
                _message.value = "Keine Kamera gefunden, bitte Kamera aktivieren"
            }
            update()
        }
        else if( _state.value == CameraViewState.initialize || _state.value == CameraViewState.ready || _state.value == CameraViewState.rolling ) {
            _autoConfirmProgress.value = 2f
            if( _imageRequested ) {
                _imageRequested = false

                _state.value = CameraViewState.ready
                _message.value = "Aufnahme..."
                takePicture()
            } else {
                _state.value = CameraViewState.ready
                _message.value = "Kamera Bereit"
            }
        }
        else if( _state.value == CameraViewState.captured ) {
            if( _rollingMode.value ) {
                _state.value = CameraViewState.rolling
                _imageRequested = true
            }
            else if( autoConfirmSeconds > 0 ) {
                if( _autoConfirmProgress.value < 1f) {
                    _autoConfirmProgress.value += 1f / (autoConfirmSeconds * 10) // Assuming update runs 10 times per second
                   if (_autoConfirmProgress.value >= 1f) {
                       autoConfirm?.acceptPicture()
                   }
                }
            }
        }
    }

    private fun takePicture() {
        try {
            val webcam = Webcam.getDefault()
            _currentImage.value = webcam.image

            if( _currentImage.value != null ) {
                _state.value = CameraViewState.captured
                _message.value = "Foto aufgenommen"
                _autoConfirmProgress.value = 0f
            } else {
                _state.value = CameraViewState.error
                _message.value = "Fehler beim Aufnehmen der Kamera, kein Bild"
            }

        } catch (e: Exception) {
            _state.value = CameraViewState.error
            _message.value = "Fehler beim Aufnehmen der Kamera, $e"
            println(e.stackTraceToString())
        }
    }

    fun stopAutoConfirm() {
        _autoConfirmProgress.value = 2f
    }

    fun requestPicture() {
        if( !rollingMode.value ) {
            _currentImage.value = null
        }
        _imageRequested = true
        _message.value = ""
        _autoConfirmProgress.value = 0f
        if( _state.value == CameraViewState.captured ) {
            _state.value = CameraViewState.ready
        }
        if( _state.value == CameraViewState.error ) {
            _state.value = CameraViewState.pending
        }
    }
}