package de.fishbyte.mrblister.app.views.calibration

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.BasicText
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
fun BlisterTemplateView(viewModel: CalibrationViewModel)
{
    val columns = viewModel.columns.collectAsState()
    val rows = viewModel.rows.collectAsState()
    val columnWidths = viewModel.columnWidths.collectAsState()
    val rowHeights = viewModel.rowHeights.collectAsState()

    val headerHeight = 40.dp
    val headerWidth = 80.dp
    val with = columnWidths.value.map { w -> w.value }.sum().dp + headerWidth
    val height = rowHeights.value.map { h -> h.value }.sum().dp + headerHeight

    fun columnWidth(column: Int) : Dp {
       return columnWidths.value[column - 1]
    }

    fun rowHeight(row: Int) : Dp {
      return rowHeights.value[row - 1]
    }

    Column(modifier = Modifier.size(with, height)) {

        // Display column headers
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(headerHeight)
        ) {
            Spacer(modifier = Modifier.width(headerWidth)) // Top-left corner spacer for alignment

            for (column in 1..columns.value) {
                Box(
                    Modifier
                        .width(columnWidth(column))
                        .fillMaxHeight()
                        .padding(2.dp)
                        .background(Color.Gray),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "Spalte ${column}",
                        style = MaterialTheme.typography.body1.copy(
                            color = Color.White,
                            fontSize = 16.sp
                        ),
                        textAlign = TextAlign.Center
                    )
                }
            }
        }

        for( row in 1..rows.value) {

            Row( modifier = Modifier
                    .fillMaxWidth()
                    .height(rowHeight(row))) {
                Box(
                    modifier = Modifier
                        .fillMaxHeight()
                        .width(headerWidth)
                        .padding(2.dp)
                        .background(Color.Gray),
                    contentAlignment = Alignment.Center
                ) {
                    BasicText(
                        modifier = Modifier.rotate(-90f),
                        text = "Zeile ${row}",
                        style = MaterialTheme.typography.body2.copy(
                            color = Color.White,
                            fontSize = 16.sp
                        )
                    )
                }

                for (column in 1..columns.value) {
                    Box(
                        modifier = Modifier
                            .size(columnWidth(column), rowHeight(row))
                            .padding(2.dp)
                            .background(if ((row + column) % 2 == 0) Color.Red else Color.Yellow),
                        contentAlignment = Alignment.TopStart
                    ) {
                        BasicText(
                            modifier = Modifier.rotate(-90f),
                            text = "${column}/${row}",
                            style = MaterialTheme.typography.body2.copy(
                                color = Color.DarkGray,
                                fontSize = 16.sp
                            )
                        )
                    }
                }
            }
        }
    }
}

