package de.fishbyte.mrblister.app.license

import java.time.LocalDate

/**
 * Utility class for validating the application license
 */
object LicenseValidator {
    
    // License expiration date (June 1, 2025)
    private val LICENSE_EXPIRATION_DATE = LocalDate.of(2026, 1, 1)
    
    /**
     * Checks if the license is valid
     * 
     * @return true if the license is valid, false otherwise
     */
    fun isLicenseValid(): Boolean {
        val currentDate = LocalDate.now()
        return currentDate.isBefore(LICENSE_EXPIRATION_DATE)
    }
    
    /**
     * Gets the license expiration date
     * 
     * @return the license expiration date
     */
    fun getLicenseExpirationDate(): LocalDate {
        return LICENSE_EXPIRATION_DATE
    }
}
