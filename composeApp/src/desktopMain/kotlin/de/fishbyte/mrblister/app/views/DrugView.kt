package de.fishbyte.mrblister.app.views

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.theme.AppTheme
import de.fishbyte.mrblister.app.views.job.BlisterJobViewModel
import org.koin.compose.getKoin

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DrugView( viewModel: BlisterJobViewModel) {

    val theme = getKoin().get<AppTheme>()

    val summary = viewModel.medication.value!!

    summary.let {

        Row {
            Text("Information", modifier = Modifier.weight(1.0f).padding(8.dp))
            Text( "Dosage", modifier = Modifier.weight(1.0f).padding(8.dp))
            Text("Taking Row", modifier = Modifier.weight(1.0f).padding(8.dp))
            Text("Taking Column", modifier = Modifier.weight(1.0f).padding(8.dp))
            Text( "Taking Date", modifier = Modifier.weight(1.0f).padding(8.dp))
            Text("Taking Time", modifier = Modifier.weight(1.0f).padding(8.dp))
        }
        it?.medications?.forEach { medication ->
            Row() {
                Text(medication.information ?: "", modifier = Modifier.weight(1.0f).padding(8.dp))
                Text( medication.dosage?.toString() ?: "", modifier = Modifier.weight(1.0f).padding(8.dp))
                Text(medication.takingRow ?: "", modifier = Modifier.weight(1.0f).padding(8.dp))
                Text(medication.takingColumn ?: "", modifier = Modifier.weight(1.0f).padding(8.dp))
                Text( medication.takingDate?.toString() ?: "", modifier = Modifier.weight(1.0f).padding(8.dp))
                Text(medication.takingTime.toString() ?: "", modifier = Modifier.weight(1.0f).padding(8.dp))
            }
        }
    }
}