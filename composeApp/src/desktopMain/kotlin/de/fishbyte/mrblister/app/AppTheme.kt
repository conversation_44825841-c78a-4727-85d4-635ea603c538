package de.fishbyte.mrblister.app

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Shapes
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

val shapes = Shapes(
    extraSmall = RoundedCornerShape(4.dp),
    small = RoundedCornerShape(8.dp),
    medium = RoundedCornerShape(16.dp),
    large = RoundedCornerShape(24.dp),
    extraLarge = RoundedCornerShape(32.dp)
)

@Immutable
class Sizes(
    val small: Dp = 1.dp
)
{

}

@Composable
fun AppTheme(
    sizes: Sizes = Sizes(),
    content: @Composable () -> Unit
) {
    MaterialTheme(
        shapes = shapes,
        content = content
    )
}