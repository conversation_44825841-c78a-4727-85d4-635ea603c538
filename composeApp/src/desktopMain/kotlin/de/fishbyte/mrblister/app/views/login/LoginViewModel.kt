package de.fishbyte.mrblister.app.views.login

import androidx.compose.ui.input.key.Key
import androidx.compose.ui.platform.LocalFocusManager
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewmodel.compose.viewModel
import de.fishbyte.mrblister.app.AppViewModel
import de.fishbyte.mrblister.app.views.KeyboardHandler
import de.fishbyte.mrblister.model.User
import de.fishbyte.mrblister.model.UserRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

class LoginViewModel(private val appViewModel: AppViewModel) : ViewModel() {

    private val _userName = MutableStateFlow<String?>(null)
    val userName: StateFlow<String?> get() = _userName.asStateFlow()

    private val _validUser = MutableStateFlow<Boolean>(false)
    val validUser: StateFlow<Boolean> get() = _validUser.asStateFlow()

    private val _password = MutableStateFlow<String?>(null)
    val password: StateFlow<String?> get() = _password.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> get() = _errorMessage.asStateFlow()

    private val _scannedUser = MutableStateFlow<User?>(null)
    val scannedUser: StateFlow<User?> get() = _scannedUser.asStateFlow()

    fun updateUserName(name: String) {
        _scannedUser.value = null
        _userName.value = name
        _errorMessage.value = null
        _validUser.value = appViewModel.userRepository.getUserByName(userName.value ?: "") != null

    }

    fun updatePassword(password: String) {
        _password.value = password
        _errorMessage.value = null
    }

    fun performLogin() {

        val userRepository = appViewModel.userRepository

        val user = userRepository.getUserByName(userName.value ?: "")
        if( user == null ) {
            _errorMessage.value = "Benutzer nicht gefunden"
        }
        else if( user.matchPassword( password.value ) ) {
            appViewModel.loginUser(user)
        } else {
            _errorMessage.value = "Falsches Passwort/PIN"
        }
    }

    companion object {
        fun present(appViewModel: AppViewModel) {

            val debug = false
            val user = if( debug ) appViewModel.userRepository.getUserByName("christian") else null
            if( user != null ) {
                appViewModel.loginUser(user)
            } else {
                appViewModel.navigation.replaceAll(LoginViewModel(appViewModel))
            }
        }
    }

}

