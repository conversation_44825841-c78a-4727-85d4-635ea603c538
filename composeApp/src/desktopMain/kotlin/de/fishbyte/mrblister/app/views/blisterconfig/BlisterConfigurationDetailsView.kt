package de.fishbyte.mrblister.app.views.blisterconfig

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.AppViewModel
import org.koin.mp.KoinPlatform.getKoin

@Composable
fun BlisterConfigurationDetailsView( viewModel: BlisterConfigurationManagementViewModel ) {
    val appViewModel = getKoin().get<AppViewModel>()

    val configurations by viewModel.configurations.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    val successMessage by viewModel.successMessage.collectAsState()
    val selectedConfiguration by viewModel.selectedConfiguration.collectAsState()
    val mode by viewModel.mode.collectAsState()

    Column {
        DetailsConfigurationForm(
                viewModel = viewModel,
                modifier = Modifier.weight(1f)
            )
    }
}

@Composable
fun DetailsConfigurationForm(
    viewModel: BlisterConfigurationManagementViewModel,
    modifier: Modifier = Modifier
) {
    val editConfigName by viewModel.editConfigName.collectAsState()
    val editConfigRows by viewModel.editConfigRows.collectAsState()
    val editConfigFlipTime by viewModel.editConfigFlipTime.collectAsState()
    val editConfigFlipDays by viewModel.editConfigFlipTime.collectAsState()
    val editConfigStations by viewModel.editConfigStations.collectAsState()

    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Text(
                "Konfiguration Details",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Form fields
            OutlinedTextField(
                value = editConfigName,
                onValueChange = viewModel::updateEditConfigName,
                label = { Text("Konfigurationsname") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                readOnly = true
            )

            Spacer(modifier = Modifier.height(16.dp))

            OutlinedTextField(
                value = editConfigRows.toString(),
                onValueChange = { value ->
                    value.toIntOrNull()?.let { viewModel.updateEditConfigRows(it) }
                },
                label = { Text("Anzahl Zeilen") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                readOnly = true
            )

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    "Zeit umgedreht: ${if (editConfigFlipTime) "Ja" else "Nein"}",
                    modifier = Modifier.padding(start = 8.dp)
                )
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    "Tage umgedreht: ${if (editConfigFlipDays) "Ja" else "Nein"}",
                    modifier = Modifier.padding(start = 8.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            OutlinedTextField(
                value = editConfigStations,
                onValueChange = viewModel::updateEditConfigStations,
                label = { Text("Stationen (kommagetrennt)") },
                modifier = Modifier.fillMaxWidth(),
                placeholder = { Text("z.B. Station 1, Station 2, Station 3") },
                readOnly = true
            )

            Spacer(modifier = Modifier.weight(1f))

            // Action buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = { viewModel.openCalibration(config = viewModel.selectedConfiguration.value!!) },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("Kalibrieren")
                }
                Button(
                    onClick = viewModel::startEditing,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("Bearbeiten")
                }
                Button(
                    onClick = { viewModel.deleteConfiguration(configToDelete = viewModel.selectedConfiguration.value!!) },
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("Löschen")
                }
            }

        }
    }
}