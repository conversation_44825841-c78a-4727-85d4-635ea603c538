package de.fishbyte.mrblister.app.views

import androidx.lifecycle.ViewModel
import de.fishbyte.mrblister.app.views.login.LoginViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.lastOrNull

class AppNavigation {

    private val _viewModelStack = MutableStateFlow<List<ViewModel>>(emptyList())
    val viewModelStack: StateFlow<List<ViewModel>> get() = _viewModelStack.asStateFlow()

    fun push(viewModel: ViewModel) {
        _viewModelStack.value = _viewModelStack.value.toMutableList().apply {
            add(viewModel)
        }
    }

    fun pop() {
        _viewModelStack.value = _viewModelStack.value.toMutableList().apply {
            removeLastOrNull()
        }
    }

    fun replaceAll(viewModel: ViewModel) {
        _viewModelStack.value = listOf(viewModel)
    }

    fun top() = viewModelStack.value.lastOrNull()
}