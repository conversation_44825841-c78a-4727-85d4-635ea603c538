package de.fishbyte.mrblister.app.views.calibration

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.material.Text
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

@Composable
fun ClickIndicatorView(viewModel: CalibrationViewModel) {
    val columns = viewModel.columns.collectAsState()
    val rows = viewModel.rows.collectAsState()
    val selectedRow = viewModel.selectedRow.collectAsState()
    val selectedColumn = viewModel.selectedColumn.collectAsState()

    Column {

        androidx.compose.material3.Text(
            "Zum Kalibrieren klicke nacheinander auf die durch den roten Kreis markierten Stellen im Blister. Ein Rechtsklick startet den Vorgang neu.",
            style = MaterialTheme.typography.headlineSmall
        )

        Spacer(modifier = Modifier.height(16.dp))

        Surface(modifier = Modifier.size(80.dp.times(columns.value) + 16.dp, 80.dp.times(rows.value) + 16.dp)) {
            Column(
                modifier = Modifier.padding(4.dp),
                verticalArrangement = Arrangement.Top
            ) {
                repeat(rows.value) { row ->
                    Row(
                        modifier = Modifier.weight(1f),
                        horizontalArrangement = Arrangement.Start
                    ) {
                        repeat(columns.value) { column ->
                            Box(
                                modifier = Modifier
                                    .weight(1f)
                                    .aspectRatio(1f)
                                    .border(1.dp, Color.LightGray)
                            )
                        }
                    }
                }
            }

            Canvas(modifier = Modifier.fillMaxSize()) {
                drawCircle(
                    color = Color.Red,
                    radius = 4.dp.toPx(),
                    center = Offset(
                        (12.dp + 80.dp.times(selectedColumn.value)).toPx(),
                        (12.dp + 80.dp.times(selectedRow.value)).toPx()
                    )
                )
            }
        }
    }
}