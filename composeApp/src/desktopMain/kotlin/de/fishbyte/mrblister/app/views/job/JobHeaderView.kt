package de.fishbyte.mrblister.app.views.job


import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.material.Text
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.theme.AppTheme
import de.fishbyte.mrblister.app.views.tools.formatShortDate
import de.fishbyte.mrblister.app.views.tools.name
import de.fishbyte.mrblister.model.BlisterJob
import org.koin.compose.getKoin
import java.time.format.DateTimeFormatter

@Composable
fun JobHeaderView(job: BlisterJob) {

    val theme = getKoin().get<AppTheme>()

    val patient = job.patient

    val index = job.getIndexLocalized()

    ElevatedCard(
        elevation = CardDefaults.cardElevation(defaultElevation = 6.dp),
        modifier = Modifier.fillMaxWidth()) {
        Column( Modifier.padding(16.dp)) {
            Text("Patient", style = MaterialTheme.typography.labelMedium)
            Row {
                Text("${name(patient)}", modifier = Modifier.weight(3f), style = MaterialTheme.typography.headlineLarge)
                if( index?.isNotEmpty() == true ) {
                    Spacer(modifier = Modifier.weight(1f))
                    Text("Blister ${index}", style = MaterialTheme.typography.headlineLarge)
                }
            }
            patient.birthday.let {
                Row {
                    Text("${formatShortDate(it)}", modifier = Modifier.weight(1f), style = MaterialTheme.typography.headlineSmall)
                }
            }
        }
    }
}