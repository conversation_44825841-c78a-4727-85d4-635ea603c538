package de.fishbyte.mrblister.app.views.job

import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.material.Text
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.theme.AppTheme
import org.koin.compose.getKoin
import java.util.stream.Collectors
import androidx.compose.ui.graphics.toComposeImageBitmap
import org.jetbrains.skia.Image
import kotlin.io.encoding.Base64
import kotlin.io.encoding.ExperimentalEncodingApi
import androidx.compose.animation.core.*
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.runtime.getValue
import androidx.compose.ui.graphics.graphicsLayer

@OptIn(ExperimentalEncodingApi::class)
fun base64ToBitmap(base64String: String): ImageBitmap {
    val imageBytes = Base64.decode(base64String)
    return Image.makeFromEncoded(imageBytes).toComposeImageBitmap()
}

fun formatDivisible( divisible: String? ) : String {
    return when (divisible) {
        "yes" -> "Ja"
        "no" -> "Nein"
        "half" -> "Halbieren"
        "third" -> "Dritteln"
        "quarter" -> "Vierteln"
        "all" -> "Ganz"
        else -> "Nicht angegeben"
    }
}

@Composable
fun MedicationInfoView( viewModel: BlisterJobViewModel) {
    val theme = getKoin().get<AppTheme>()

    val patient = viewModel.job.patient
    val job = viewModel.job

    val medication = viewModel.medication.value!!
    val drug = medication.drug

    val medicationInfo = medication.medications.stream().map { it.information }.filter { !it.isNullOrBlank() }.distinct()
        .collect(Collectors.joining(";"))

    val infiniteTransition = rememberInfiniteTransition()
    val alpha by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = 0f,
        animationSpec = infiniteRepeatable(
            animation = tween(500, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        )
    )

    ElevatedCard(
        elevation = CardDefaults.cardElevation(defaultElevation = 6.dp),
        modifier = Modifier.fillMaxWidth()) {
        Column(modifier = Modifier.padding(16.dp)) {

            Text(
                "Medikament",
                style = MaterialTheme.typography.labelMedium
            )

            Text(drug.name ?: "",
                style = MaterialTheme.typography.headlineLarge
            )


            if (medicationInfo?.lowercase()?.contains("blister") == true) {
                Text(
                    medicationInfo,
                    modifier = Modifier.graphicsLayer(alpha = alpha),
                    style = MaterialTheme.typography.headlineMedium
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            Row {
                drug.fotoData?.let { base6Data ->
                    val bitmap = base64ToBitmap(base6Data)
                    Image(bitmap = bitmap, contentDescription = "Arzneimittel Foto", modifier = Modifier.size(200.dp).padding(6.dp))

                    Column(modifier = Modifier.fillMaxWidth()) {

                        val labelSize = MaterialTheme.typography.titleLarge.fontSize
                        val textSize = MaterialTheme.typography.titleLarge.fontSize
                        drug.color?.let {
                            Row {
                                Text(
                                    "Farbe",
                                    modifier = Modifier.weight(1f).padding(6.dp),
                                    fontSize = labelSize
                                )
                                Text(
                                    it,
                                    modifier = Modifier.weight(3f).padding(6.dp),
                                    fontSize = textSize
                                )
                            }
                        }

                        drug.form?.let {
                            Row {
                                Text(
                                    "Form",
                                    modifier = Modifier.weight(1f).padding(6.dp),
                                    fontSize = labelSize
                                )
                                Text(
                                    it,
                                    modifier = Modifier.weight(3f).padding(6.dp),
                                    fontSize = textSize
                                )
                            }
                        }

                        Row {
                            Text(
                                "Teilbar",
                                modifier = Modifier.weight(1f).padding(6.dp),
                                fontSize = labelSize
                            )
                            Text(
                                formatDivisible(drug.divisible),
                                modifier = Modifier.weight(3f).padding(6.dp),
                                fontSize = textSize
                            )
                        }

                        drug.frontMarking?.let {
                            Row {
                                Text(
                                    "Front",
                                    modifier = Modifier.weight(1f).padding(6.dp),
                                    fontSize = labelSize
                                )
                                Text(
                                    it,
                                    modifier = Modifier.weight(3f).padding(6.dp),
                                    fontSize = textSize
                                )
                            }
                        }

                        drug.rearMarking?.let {
                            Row {
                                Text(
                                    "Rücken",
                                    modifier = Modifier.weight(1f).padding(6.dp),
                                    fontSize = labelSize
                                )
                                Text(
                                    it,
                                    modifier = Modifier.weight(3f).padding(6.dp),
                                    fontSize = textSize
                                )
                            }
                        }

                        /*
                    drug.packingTypes?.packingTypeList?.let {
                        for (pckg in it) {
                            Row {
                                Text("PZN", modifier = Modifier.weight(1f).padding(8.dp))
                                Text(pckg.id + "(" + pckg.size + " Stk.)", modifier = Modifier.weight(3f).padding(8.dp))
                            }
                        }
                    }

                     */
                    }
                }
            }
        }
    }
}