package de.fishbyte.mrblister.settings

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.material.Text
import androidx.compose.runtime.*

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun SettingsWorkflow() {

    var checked by remember { mutableStateOf(true) }

/*
    val scope = rememberCoroutineScope()

    Box( modifier = Modifier.padding(16.dp) ) {
        Column(modifier = Modifier.padding(8.dp)) {

            val state = rememberTextFieldState("e:\\")

            val countries = listOf(
                "Japan",
                "France",
                "Mexico",
                "Indonesia",
                "Egypt"
            )
            var showCountries by remember { mutableStateOf(false) }
            var timeAtLocation by remember { mutableStateOf("No location selected") }

            Column(modifier = Modifier.padding(20.dp)) {
                Text(
                    timeAtLocation,
                    style = TextStyle(fontSize = 20.sp),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth().align(Alignment.CenterHorizontally)
                )
                Row(modifier = Modifier.padding(start = 20.dp, top = 10.dp)) {
                    DropdownMenu(
                        expanded = showCountries,
                        onDismissRequest = { showCountries = false }
                    ) {
                        countries.forEach { country ->
                            DropdownMenuItem(
                                onClick = {
                                    timeAtLocation = country
                                    showCountries = false
                                }
                            ) {
                                Text(country)
                            }
                        }
                    }
                }

                Button(modifier = Modifier.padding(start = 20.dp, top = 10.dp),
                    onClick = { showCountries = !showCountries }) {
                    Text("Select Location")
                }
            }

            /*
            TextField("e:\\", onValueChange = {}, enabled = false, label = { Text("Path") } )

            Box( modifier = Modifier.background(MaterialTheme.colorScheme.secondary).border(1.dp, MaterialTheme.colorScheme.onPrimary, MaterialTheme.shapes.extraSmall) ) {
                Column( modifier = Modifier.padding(8.dp) ) {
                    Text("some text", style = MaterialTheme.typography.labelLarge)
                    Text( "More Text", style = MaterialTheme.typography.bodyLarge)
                }
            }

             */


        }
    }

 */
    Text("test")

}

fun editPath() {

}