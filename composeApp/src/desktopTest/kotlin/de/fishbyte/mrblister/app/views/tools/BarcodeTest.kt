package de.fishbyte.mrblister.app.views.tools


import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.format.DateTimeFormatter

class BarcodeTest {

    @Test
    fun testEmptyBarcode() {
        val barcode = Barcode.parse("")
        assertEquals(BarcodeType.UNKNOWN, barcode.type)
        assertEquals("", barcode.rawValue)
        assertNull(barcode.pzn)
    }

    @Test
    fun testSimplePZN() {
        // Test 7-digit PZN
        val pzn7 = Barcode.parse("1234567")
        assertEquals(BarcodeType.PZN, pzn7.type)
        assertEquals("1234567", pzn7.pzn)

        // Test 8-digit PZN
        val pzn8 = Barcode.parse("12345678")
        assertEquals(BarcodeType.PZN, pzn8.type)
        assertEquals("12345678", pzn8.pzn)

        // Test PZN with whitespace
        val pznWithSpace = Barcode.parse(" 1234567 ")
        assertEquals(BarcodeType.PZN, pznWithSpace.type)
        assertEquals("1234567", pznWithSpace.pzn)
    }

    @Test
    fun testGenericBarcode() {
        val barcode = Barcode.parse("ABC123456")
        assertEquals(BarcodeType.GENERIC, barcode.type)
        assertEquals("ABC123456", barcode.rawValue)
    }

    @Test
    fun testGS1DataMatrix() {
        // GS1 DataMatrix with GTIN, batch, expiry, and serial
        val gs1Code = "010415016663636321ET4K3SJKG9E8HT\u001D1727033110DRE05025A"
        val barcode = Barcode.parse(gs1Code)
        assertEquals("16663636", barcode.pzn)
    }

    @Test
    fun testGS1DataMatrixWithPZNinNTIN() {
        // GS1 DataMatrix with NTIN (containing PZN), batch, expiry, and serial
        val gs1Code = "01041501234567831017032110ABC123" + "\u001D" + "21SERIAL123456"
        val barcode = Barcode.parse(gs1Code)

        assertEquals(BarcodeType.GS1_DATAMATRIX, barcode.type)
        assertEquals("1234567", barcode.pzn)
        assertEquals("04150123456783", barcode.gtin)
        assertEquals("10ABC123", barcode.batchNumber)
        assertEquals(LocalDate.of(2017, 3, 21), barcode.expiryDate)
        assertEquals("SERIAL123456", barcode.serialNumber)
    }

    @Test
    fun testGS1DataMatrixWithDirectPZN() {
        // GS1 DataMatrix with direct PZN encoding using AI "09"
        val gs1Code = "091234567" + "\u001D" + "17220331" + "\u001D" + "10LOT12345"
        val barcode = Barcode.parse(gs1Code)

        assertEquals(BarcodeType.GS1_DATAMATRIX, barcode.type)
        assertEquals("1234567", barcode.pzn)
        assertEquals("LOT12345", barcode.batchNumber)
        assertEquals(LocalDate.of(2022, 3, 31), barcode.expiryDate)
    }

    @Test
    fun testGS1DataMatrixWithPrefixAndGS() {
        // GS1 DataMatrix with ]d2 prefix and GS character
        val gs1Code = "]d2010415012345678" + "\u001D" + "17220331" + "\u001D" + "10LOT12345"
        val barcode = Barcode.parse(gs1Code)

        assertEquals(BarcodeType.GS1_DATAMATRIX, barcode.type)
        assertEquals("1234567", barcode.pzn)
        assertEquals("04150123456783", barcode.gtin)
        assertEquals("LOT12345", barcode.batchNumber)
        assertEquals(LocalDate.of(2022, 3, 31), barcode.expiryDate)
    }

    @Test
    fun testGS1DataMatrixWithEmbeddedPZN() {
        // GS1 DataMatrix with PZN embedded somewhere in the GTIN
        val gs1Code = "0198765432123456" + "\u001D" + "17230630"
        val barcode = Barcode.parse(gs1Code)

        assertEquals(BarcodeType.GS1_DATAMATRIX, barcode.type)
        assertEquals("1234567", barcode.pzn)
        assertEquals(LocalDate.of(2023, 6, 30), barcode.expiryDate)
    }

    @Test
    fun testGS1DataMatrixWithASCII29() {
        // GS1 DataMatrix with ASCII 29 as separator
        val gs1Code = "0104150123456783" + 29.toChar() + "17240228" + 29.toChar() + "10BATCH2024"
        val barcode = Barcode.parse(gs1Code)

        assertEquals(BarcodeType.GS1_DATAMATRIX, barcode.type)
        assertEquals("123456783", barcode.pzn)
        assertEquals("BATCH2024", barcode.batchNumber)
        assertEquals(LocalDate.of(2024, 2, 28), barcode.expiryDate)
    }

    @Test
    fun testMixedSeparators() {
        // GS1 DataMatrix with mixed separator types
        val gs1Code = "0104150123456783" + "\u001D" + "17240228" + 29.toChar() + "10BATCH2024"
        val barcode = Barcode.parse(gs1Code)

        assertEquals(BarcodeType.GS1_DATAMATRIX, barcode.type)
        assertEquals("123456783", barcode.pzn)
        assertEquals("BATCH2024", barcode.batchNumber)
        assertEquals(LocalDate.of(2024, 2, 28), barcode.expiryDate)
    }

    @Test
    fun testInvalidExpiryDate() {
        // GS1 DataMatrix with invalid expiry date
        val gs1Code = "0104150123456783" + "\u001D" + "17999999" + "\u001D" + "10BATCH2024"
        val barcode = Barcode.parse(gs1Code)

        assertEquals(BarcodeType.GS1_DATAMATRIX, barcode.type)
        assertEquals("1234567", barcode.pzn)
        assertEquals("BATCH2024", barcode.batchNumber)
        assertNull(barcode.expiryDate)
    }

    @Test
    fun testPaddedPZN() {
        // GS1 DataMatrix with zero-padded PZN in NTIN
        val gs1Code = "01041500012345631017032110ABC123"
        val barcode = Barcode.parse(gs1Code)

        assertEquals(BarcodeType.GS1_DATAMATRIX, barcode.type)
        assertEquals("0012345", barcode.pzn)
        assertEquals("04150001234563", barcode.gtin)
    }

    @Test
    fun testRealWorldExample1() {
        // Real-world example from a medication package
        val gs1Code = "010415780173331117240630108675A21123ABC"
        val barcode = Barcode.parse(gs1Code)

        assertEquals(BarcodeType.GS1_DATAMATRIX, barcode.type)
        assertEquals("7801733", barcode.pzn)
        assertEquals("04157801733311", barcode.gtin)
        assertEquals("8675A", barcode.batchNumber)
        assertEquals(LocalDate.of(2024, 6, 30), barcode.expiryDate)
        assertEquals("123ABC", barcode.serialNumber)
    }

    @Test
    fun testRealWorldExample2() {
        // Another real-world example with different format
        val gs1Code = "]d2010415123456789" + "\u001D" + "17250228" + "\u001D" + "10A12B34" + "\u001D" + "21SERIAL9876"
        val barcode = Barcode.parse(gs1Code)

        assertEquals(BarcodeType.GS1_DATAMATRIX, barcode.type)
        assertEquals("1234567", barcode.pzn)
        assertEquals("04151234567893", barcode.gtin)
        assertEquals("A12B34", barcode.batchNumber)
        assertEquals(LocalDate.of(2025, 2, 28), barcode.expiryDate)
        assertEquals("SERIAL9876", barcode.serialNumber)
    }
}