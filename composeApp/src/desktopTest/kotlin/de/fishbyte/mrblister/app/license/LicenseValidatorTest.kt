package de.fishbyte.mrblister.app.license

import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.time.LocalDate

class LicenseValidatorTest {

    @Test
    fun `license should be valid before expiration date`() {
        // The license should be valid until June 1, 2025
        assertTrue(LicenseValidator.isLicenseValid())
        
        // Verify the expiration date is set correctly
        val expectedExpirationDate = LocalDate.of(2025, 6, 1)
        assertTrue(LicenseValidator.getLicenseExpirationDate() == expectedExpirationDate)
    }
}
