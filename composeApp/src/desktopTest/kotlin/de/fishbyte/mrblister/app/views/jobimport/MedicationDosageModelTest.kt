
package de.fishbyte.mrblister.app.views.jobimport

import MedicationDosageModel
import de.fishbyte.mrblister.model.Medication
import de.fishbyte.mrblister.model.Packages
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime
import kotlin.random.Random

class MedicationDosageModelTest {

    fun medication( takingDay: DayOfWeek, takingHour: Int, dosage: Double ) : MedicationDosageModel {

        val takingDate = LocalDate.of(2025, 1, 5+takingDay.value)
        val takingTime = LocalTime.of(takingHour, 0)

        return MedicationDosageModel.fromMedication( Medication("1234","1234", dosage, takingDate, "Undefined", takingTime, "Undefined", "info", Packages(
            emptyList()
        )))
    }

    @Test
    fun `test simple canMergeDays`() {
        val medMO = medication(DayOfWeek.MONDAY, 12, 1.0)
        val medTU = medication(DayOfWeek.TUESDAY, 12, 1.0)
        val medWE = medication(DayOfWeek.WEDNESDAY, 12, 1.0)
        val medTH = medication(DayOfWeek.THURSDAY, 12, 1.0)
        val medFR = medication(DayOfWeek.FRIDAY, 12, 2.0)
        val medSA = medication(DayOfWeek.SATURDAY, 12, 1.0)
        val medSU = medication(DayOfWeek.SUNDAY, 12, 1.0)

        assertTrue(medMO.canMergeDays(medTU))
        assertTrue(medTU.canMergeDays(medMO))
        assertFalse(medMO.canMergeDays(medWE))
        assertFalse(medWE.canMergeDays(medMO))
        assertFalse(medTH.canMergeDays(medFR))
        assertFalse(medMO.canMergeDays(medSU))  // monday can't connect with sunday
        assertFalse(medSU.canMergeDays(medMO))  // monday can't connect with sunday
    }

    @Test
    fun `test mergeWith`() {

        val medMO = medication(DayOfWeek.MONDAY, 12, 1.0)
        val medTU = medication(DayOfWeek.TUESDAY, 12, 1.0)
        val medFR = medication(DayOfWeek.FRIDAY, 12, 2.0)
        val medSA = medication(DayOfWeek.SATURDAY, 12, 1.0)

        val medMOTU = medMO.mergeWith(medTU)
/*
        assertTrue(medMOTU.weekDayRanges.start == DayOfWeek.MONDAY)
        assertTrue(medMOTU.weekDayRange.endInclusive == DayOfWeek.TUESDAY)
        assertTrue(medMOTU.dosages[LocalTime.of(12, 0)] == 1.0)
        assertTrue(medMOTU.dosages.size == 1)

        val medTUMO = medTU.mergeWith(medMO)

        assertTrue(medTUMO.weekDayRange.start == DayOfWeek.MONDAY)
        assertTrue(medTUMO.weekDayRange.endInclusive == DayOfWeek.TUESDAY)

        assertTrue(medTUMO.dosages[LocalTime.of(12, 0)] == 1.0)
        assertTrue(medTUMO.dosages.size == 1)

        // should fail because the dosage is not matching
        try {
            medFR.mergeWith(medSA)
        } catch (e: Exception) {
            assertTrue(true)
        }

 */
    }

    @Test
    fun `test mergeDays`() {
        val medMO = medication(DayOfWeek.MONDAY, 12, 1.0)
        val medTU = medication(DayOfWeek.TUESDAY, 12, 1.0)
        val medWE = medication(DayOfWeek.WEDNESDAY, 12, 1.0)
        val medTH = medication(DayOfWeek.THURSDAY, 12, 1.0)
        val medFR = medication(DayOfWeek.FRIDAY, 12, 2.0)
        val medSA = medication(DayOfWeek.SATURDAY, 12, 1.0)
        val medSU = medication(DayOfWeek.SUNDAY, 12, 1.0)

        assertTrue(medSA.canMergeDays(medSU))

        val meds = listOf(medMO, medTH, medWE, medFR, medSA, medSU, medTU)

        val merged = MedicationDosageModel.mergeDays(meds)
/*
        assertTrue(merged.size == 3)
        assertTrue(merged[0].weekDayRange.start == DayOfWeek.MONDAY)
        assertTrue(merged[0].weekDayRange.endInclusive == DayOfWeek.THURSDAY)
        assertTrue(merged[0].dosages[LocalTime.of(12, 0)] == 1.0)
        assertTrue(merged[0].dosages.size == 1)

        assertTrue(merged[1].weekDayRange.start == DayOfWeek.FRIDAY)
        assertTrue(merged[1].weekDayRange.endInclusive == DayOfWeek.FRIDAY)
        assertTrue(merged[1].dosages[LocalTime.of(12, 0)] == 2.0)
        assertTrue(merged[1].dosages.size == 1)

        assertTrue(merged[2].weekDayRange.start == DayOfWeek.SATURDAY)
        assertTrue(merged[2].weekDayRange.endInclusive == DayOfWeek.SUNDAY)
        assertTrue(merged[2].dosages[LocalTime.of(12, 0)] == 1.0)
        assertTrue(merged[2].dosages.size == 1)
*/
    }

    @Test
    fun `test mergeDaysWithOverlapping`() {
        val medMO1 = medication(DayOfWeek.MONDAY, 12, 1.0)
        val medTU1 = medication(DayOfWeek.TUESDAY, 12, 1.0)
        val medWE1 = medication(DayOfWeek.WEDNESDAY, 12, 1.0)
        val medTH1 = medication(DayOfWeek.THURSDAY, 12, 1.0)
        val medSA1 = medication(DayOfWeek.SATURDAY, 12, 1.0)
        val medSU1 = medication(DayOfWeek.SUNDAY, 12, 1.0)
        val medMO2 = medication(DayOfWeek.MONDAY, 18, 2.0)
        val medTU2 = medication(DayOfWeek.TUESDAY, 18, 2.0)
        val medWE2 = medication(DayOfWeek.WEDNESDAY, 18, 2.0)
        val medTH2 = medication(DayOfWeek.THURSDAY, 18, 1.0)
        val medFR2 = medication(DayOfWeek.FRIDAY, 18, 2.0)
        val medSA2 = medication(DayOfWeek.SATURDAY, 18, 3.0)
        val medSU2 = medication(DayOfWeek.SUNDAY, 18, 2.0)


        //         Mo Di Mi Do Fr Sa So
        //  12:00  01 01 01 01 -- 01 01
        //  18:00  02 02 02 01 02 03 02

        // Mo-Mi, So 01 02
        // Do    01 01
        // Fr    -- 02
        // Sa    01 03

        val meds = listOf(medMO1, medTH1, medWE1, medFR2, medSA1, medSU1, medTU1, medTU2, medMO2,medWE2, medTH2, medSA2, medSU2)

        val random = Random(42)

        // shuffle 10 times to check if the result is always the same
        for( i in 0..10) {

            val merged = MedicationDosageModel.mergeDays(meds.shuffled(random))

            assertTrue(merged.size == 4)
/*            assertTrue(merged[0].weekDayRange.start == DayOfWeek.MONDAY)
            assertTrue(merged[0].weekDayRange.endInclusive == DayOfWeek.WEDNESDAY)
            assertTrue(merged[0].dosages[LocalTime.of(12, 0)] == 1.0)
            assertTrue(merged[0].dosages[LocalTime.of(18, 0)] == 2.0)
            assertTrue(merged[0].dosages.size == 2)

            assertTrue(merged[1].weekDayRange.start == DayOfWeek.THURSDAY)
            assertTrue(merged[1].weekDayRange.endInclusive == DayOfWeek.THURSDAY)
            assertTrue(merged[1].dosages[LocalTime.of(12, 0)] == 1.0)
            assertTrue(merged[1].dosages[LocalTime.of(18, 0)] == 1.0)
            assertTrue(merged[1].dosages.size == 2)

            assertTrue(merged[2].weekDayRange.start == DayOfWeek.FRIDAY)
            assertTrue(merged[2].weekDayRange.endInclusive == DayOfWeek.FRIDAY)
            assertTrue(merged[2].dosages[LocalTime.of(18, 0)] == 2.0)
            assertTrue(merged[2].dosages.size == 1)

            assertTrue(merged[3].weekDayRange.start == DayOfWeek.SATURDAY)
            assertTrue(merged[3].weekDayRange.endInclusive == DayOfWeek.SUNDAY)
            assertTrue(merged[3].dosages[LocalTime.of(12, 0)] == 1.0)
            assertTrue(merged[3].dosages[LocalTime.of(18, 0)] == 3.0)
            assertTrue(merged[3].dosages.size == 2)

 */
        }
    }
}
