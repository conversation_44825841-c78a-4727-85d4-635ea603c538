package de.fishbyte.mrblister.util

import java.io.FileInputStream
import java.util.Properties

object VersionInfo {
    private val properties = Properties()

    val buildNumber: String

    init {
        buildNumber = "18" /*try {

            val properties = Properties().apply {
                javaClass.getResourceAsStream("/version.properties")?.use {
                    load(it)
                }
            }
            properties.getProperty("build.number", "unknown")
        } catch (e: Exception) {
            println("Failed to load version properties: ${e.message}")
            "unknown"
        }

 */
    }
}
